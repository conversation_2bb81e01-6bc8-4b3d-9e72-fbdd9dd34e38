# GitHub Actions Workflows

This directory contains GitHub Actions workflows for code quality checks and deployments.

## Workflows

### Code Quality Check
- Runs on every push to main and on all pull requests
- Checks code formatting with Black
  - On the main branch, it will automatically apply formatting if needed
  - On pull requests, it will report formatting issues without failing
- Performs linting with flake8

### Deploy to AWS Lambda
- Runs on push to main branch or manually via workflow_dispatch
- Can deploy the main handler, simulator, or both
- Uses AWS credentials from GitHub Secrets
- Executes the existing deploy.sh and deploy_simulator.sh scripts

## Setting Up Required Secrets

For the deployment workflow to function properly, you need to set up the following secrets in your GitHub repository:

1. Go to your repository on GitHub
2. Click on "Settings" > "Secrets and variables" > "Actions"
3. Add the following repository secrets:

   - `AWS_ACCESS_KEY_ID`: Your AWS access key
   - `AWS_SECRET_ACCESS_KEY`: Your AWS secret key

The IAM user associated with these credentials should have permissions for:
- ECR (Elastic Container Registry)
- Lambda
- Any other AWS services used in the deployment scripts
