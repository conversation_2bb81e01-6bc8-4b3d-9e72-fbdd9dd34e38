# Hypernative Alert Handler

This repository contains the Lambda-based handler and utility code for processing Hypernative alerts, agent workflows, and ForDefi transactions. It implements a minimal asynchronous architecture to avoid API Gateway timeouts while keeping infrastructure simple.

## Quick Start (.env example)

Create a `.env` file (or set environment variables in your deployment environment):

```
FORDEFI_BEARER_TOKEN = <YOUR_BEARER_TOKEN>

<CHAIN_SYMBOL>_PROVIDER_URL = <RPC_HTTP_PROVIDER_URL>
```

Replace placeholders with your own configuration.

---

## Minimal Asynchronous Lambda Architecture

To prevent API Gateway timeouts, the handler uses a minimal asynchronous pattern while keeping a single Lambda function.

### Architecture Overview

One Lambda function handles both webhook reception and agent processing:

1. Webhook reception: returns HTTP 200 within ~1–2 seconds after starting async processing.
2. Async processing: the same Lambda function invokes itself asynchronously to process agents.

### Components Added

- DynamoDB table: `hypernative-idempotency` for duplicate prevention
- IAM policies: DynamoDB access and Lambda self-invocation permissions
- Code change: `hypernative_handler.py` invokes itself asynchronously and returns quickly

No additional Lambda functions or complex infrastructure are needed.

### How It Works

1. Webhook arrives at API Gateway → Lambda handler.
2. Lambda handler:
	 - Generates an idempotency key
	 - Checks DynamoDB for duplicates
	 - Handles ForDefi transactions synchronously (fast)
	 - For agent processing: invokes itself asynchronously
	 - Returns HTTP 200 within ~1–2 seconds
3. Async Lambda invocation processes agents (can take minutes)

### Idempotency

- Uses `fordefi-transaction-id` when available
- Falls back to SHA256 hash of event body
- 24-hour TTL prevents indefinite storage
- Conditional DynamoDB writes prevent duplicates

### Error Handling

- Failed async invocations are handled by Lambda’s built-in retry mechanism
- All processing logs go to the same CloudWatch log group
- Main handler removes idempotency record if async invocation fails

### Monitoring

- CloudWatch logs group: `/aws/lambda/hypernative_handler` (both webhook reception and async processing)

Key metrics to monitor:

- API Gateway response times (should be < 2 seconds)
- DynamoDB throttling
- Lambda error rates and durations
- Lambda concurrent executions

Alarms:

- Existing error alarms continue to work
- Monitor Lambda throttling if many concurrent async invocations occur

### Configuration

- Environment variable: `cloud_env=hypernative_handler`
- DynamoDB table:
	- Billing mode: pay-per-request (auto-scales)
	- TTL: 24 hours for idempotency records

---

## Deployment

Prerequisites: Terraform configured for your AWS account and region.

### 1) Deploy Infrastructure

```bash
terraform plan
terraform apply
```

This creates the DynamoDB table and the required IAM permissions (DynamoDB access, Lambda self-invocation).

### 2) Deploy Updated Lambda Handler

```bash
./deploy.sh
```

This deploys the main handler with async self-invocation logic.

### 3) Verify Deployment

```bash
aws dynamodb list-tables | grep hypernative-idempotency

# Test the endpoint response time
time curl -X POST -H "x-api-key: YOUR_API_KEY" YOUR_API_GATEWAY_URL
```

---

## Testing

### Test Fast Response

```bash
time curl -X POST \
	-H "x-api-key: YOUR_API_KEY" \
	-H "Content-Type: application/json" \
	-d '{"body": "{\"data\": \"{\\\"customAgents\\\": [], \\\"watchlists\\\": []}\"}"}' \
	https://YOUR_API_GATEWAY_URL/prod/hypernative_handler
```

### Test Idempotency

```bash
# Send the same request twice; it should process only once.
aws dynamodb scan --table-name hypernative-idempotency
```

### Monitor Processing

```bash
aws logs tail /aws/lambda/hypernative_handler --follow
```

---

## Rollback Plan

1. Revert Lambda code: deploy a previous version without async self-invocation.
2. Keep the DynamoDB table: harmless to keep and still useful for idempotency.

---

## Performance Expectations

- API response time: < 2 seconds (previously > 30 seconds)
- Agent processing: unchanged (can take minutes for blockchain operations)
- Throughput: higher due to async processing
- Reliability: improved via idempotency and Lambda’s built-in retries

---

## Troubleshooting

Common issues:

1) IAM permissions: ensure the Lambda execution role has DynamoDB and Lambda invoke permissions.
2) Concurrent executions: monitor Lambda concurrency limits with many async invocations.
3) DynamoDB throttling: monitor DynamoDB metrics under high request volume.

Helpful commands:

```bash
# Check IAM permissions
aws iam list-attached-role-policies --role-name lambda_exec_role

# Check DynamoDB access
aws dynamodb put-item \
	--table-name hypernative-idempotency \
	--item '{"idempotency_key": {"S": "test"}, "timestamp": {"N": "1640995200"}}'

# Test Lambda self-invocation
aws lambda invoke \
	--function-name hypernative_handler \
	--invocation-type Event \
	--payload '{"async_processing": true, "body": "{\"data\": \"{}\"}"}' \
	response.json
```