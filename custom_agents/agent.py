import sys
import os

sys.path.append(os.getcwd())

from abc import ABC, abstractmethod
from helpers.web3_utils.web3_connector import Web3Connector
from helpers.web3_utils.web3_contract import Web3Contract
from helpers.provider_url import PROVIDER_URL


# This should be the abstract class / interface for all custom agent solutions we create
class Agent(ABC):
    def __init__(self, chain_name, contract_address, contract_abi):
        self.chain_name = chain_name
        self.contract_address = contract_address
        self.contract_abi = contract_abi

        self.client = Web3Connector(PROVIDER_URL[self.chain_name]).get_client()
        self.contract = Web3Contract(
            self.client, self.contract_address, self.contract_abi
        )

    @abstractmethod
    def generate_transaction(self):
        contract_address = self.contract_address
        abi = self.contract_abi
        function_name = ""
        params = None
        return contract_address, abi, function_name, params

    @abstractmethod
    def simulate_transaction(
        self, contract_address, abi, from_address, function_name, params
    ):
        pass

    @abstractmethod
    def execute(self):
        pass
