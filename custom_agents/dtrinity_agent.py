from custom_agents.agent import Agent
from agent_info.dtrinity_agent_info import get_ABI, get_address, get_chain_name
from helpers.web3_utils.web3_contract import Web3Contract
from helpers.fordefi_transaction_helpers import (
    build_and_send_transaction,
    approve_token,
)
from telegram_notifier import TelegramNotifier
from helpers.simulate_transaction import simulate_transaction

from helpers.log import get_logger

logger = get_logger(__name__)


class DTrinityAgent(Agent):
    def __init__(self):
        self.static_param_key = "DTrinityAgent"
        self.chain_name = get_chain_name()
        self.address = get_address()
        self.abi = get_ABI()
        super().__init__(self.chain_name, self.address, self.abi)
        self.set_params()

    def set_params(self):
        from static_params_index import static_params_index

        from_address = static_params_index[self.static_param_key]["account"]

        atoken_contract = Web3Contract(
            self.client,
            static_params_index[self.static_param_key]["atoken_address"],
            static_params_index[self.static_param_key]["atoken_abi"],
        )
        self.scaled_balance = atoken_contract.call(
            "scaledBalanceOf",
            [from_address],
        )
        pool_address_provider_contract = Web3Contract(
            self.client,
            static_params_index[self.static_param_key]["pool_address_provider_address"],
            static_params_index[self.static_param_key]["pool_address_provider_abi"],
        )
        pool_data_provider_address = pool_address_provider_contract.call(
            "getPoolDataProvider",
        )
        pool_data_provider_contract = Web3Contract(
            self.client,
            pool_data_provider_address,
            static_params_index[self.static_param_key]["pool_data_provider_abi"],
        )
        reserve_data = pool_data_provider_contract.call(
            "getReserveData",
            [static_params_index[self.static_param_key]["dusd_address"]],
        )
        total_debt = reserve_data[3] + reserve_data[4]
        available_liquidity = reserve_data[2] - total_debt

        self.withdraw_amount = int(
            min(
                self.scaled_balance,
                available_liquidity,
            )
        )

    def generate_transaction(self):
        from static_params_index import static_params_index

        contract_address = self.address
        abi = self.abi
        function_name = "withdraw"
        params = [
            static_params_index[self.static_param_key]["dusd_address"],
            self.withdraw_amount,
            static_params_index[self.static_param_key]["account"],
        ]

        return contract_address, abi, function_name, params

    def simulate_transaction(self, client):
        from static_params_index import static_params_index

        from_address = static_params_index[self.static_param_key]["account"]

        # Prepare transaction
        contract_address, abi, function_name, params = self.generate_transaction()
        contract = Web3Contract(
            client,
            contract_address,
            abi,
        )
        contract_function = getattr(contract.contract.functions, function_name)

        # Simulate transaction
        logger.info("Simulating withdraw transaction...")
        result = simulate_transaction(client, contract_function, params, from_address)
        if result:
            logger.info("Withdraw transaction simulation: successful")
        else:
            logger.error("Withdraw transcation simulation: failed")
            return result

        # Confirm expected state
        atoken_cotnract = Web3Contract(
            client,
            static_params_index[self.static_param_key]["atoken_address"],
            static_params_index[self.static_param_key]["atoken_abi"],
        )
        scaled_balance = atoken_cotnract.call(
            "scaledBalanceOf",
            [from_address],
        )
        if scaled_balance >= self.scaled_balance:
            logger.error(
                f"Scaled balance after withdrawal did not decrease: {scaled_balance} (after) >= {self.scaled_balance} (before)"
            )
            return False
        else:
            logger.info(f"Simulation successful: Reached expected state")
            return True

    def execute(self):
        from static_params_index import static_params_index

        from_address = static_params_index[self.static_param_key]["account"]

        try:
            contract_address, abi, function_name, params = self.generate_transaction()
            contract = Web3Contract(self.client, contract_address, abi)
            contract_function = getattr(contract.contract.functions, function_name)
            logger.info(f"Contract funcitons: {contract_function}")

            tx_hash = build_and_send_transaction(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                contract_function,
                params,
                from_address,
            )
            TelegramNotifier.notify(
                f"Withdraw transaction sent for {self.__class__.__name__}: {tx_hash}"
            )
            return tx_hash
        except Exception as e:
            logger.error(f"Couldn't withdraw: {e}")
            return
