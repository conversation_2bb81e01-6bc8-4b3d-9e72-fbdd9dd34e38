import time
from custom_agents.agent import Agent
from agent_info.aerodrome_agent_info import (
    get_ABI,
    get_address,
    get_chain_name,
)
from helpers.web3_utils.web3_contract import Web3Contract
from helpers.fordefi_transaction_helpers import (
    build_and_send_transaction,
    approve_token,
)
from helpers.simulate_transaction import simulate_transaction
from helpers.web3_utils.erc20ABI import erc20_ABI

from helpers.log import get_logger

logger = get_logger(__name__)


class AerodromeAgent(Agent):
    def __init__(self):
        self.static_param_key = "AerodromeAgent"
        self.chain_name = get_chain_name()
        self.address = get_address()
        self.abi = get_ABI()
        super().__init__(self.chain_name, self.address, self.abi)
        self.set_params()

    def set_params(self):
        from static_params_index import static_params_index

        # TODO: Uncomment when staking is implemented
        # # Initialize the staking contracts
        # self.staking_contract = Web3Contract(
        #     self.client,
        #     static_params_index[self.static_param_key]["staking_address"],
        #     static_params_index[self.static_param_key]["staking_abi"],
        # )

        # # Set the static parameters for unstaking
        # self.staked_amount = self.staking_contract.call(
        #     "balanceOf", [static_params_index[self.static_param_key]["account"]]
        # )

        # Initialize the LP contract
        self.lp_contract = Web3Contract(
            self.client,
            static_params_index[self.static_param_key]["lp_address"],
            static_params_index[self.static_param_key]["lp_abi"],
        )

        # Set the static parameters for withdrawing the LP
        self.liquidity = self.lp_contract.call(
            "balanceOf", [static_params_index[self.static_param_key]["account"]]
        )

        self.tokenA = static_params_index[self.static_param_key]["tokenA"]
        self.tokenB = static_params_index[self.static_param_key]["tokenB"]
        self.stable = static_params_index[self.static_param_key]["stable"]

        self.amountAMin = 0
        self.amountBMin = 0

        self.to = static_params_index[self.static_param_key]["account"]

        self.deadline = int(time.time()) + 600

    def generate_transaction(self):
        # Implement the logic to generate a unstaking transaction
        from static_params_index import static_params_index

        contract_address = static_params_index[self.static_param_key]["staking_address"]
        abi = static_params_index[self.static_param_key]["staking_abi"]
        function_name = "withdraw"
        params = [self.staked_amount]

        return contract_address, abi, function_name, params

    def generate_transaction2(self):
        # Implement the logic to generate a liquidity removal transaction
        contract_address = self.address
        abi = self.abi
        function_name = "removeLiquidity"
        params = [
            self.tokenA,
            self.tokenB,
            self.stable,
            self.liquidity,
            self.amountAMin,
            self.amountBMin,
            self.to,
            self.deadline,
        ]

        return contract_address, abi, function_name, params

    def simulate_transaction(self, client):
        from static_params_index import static_params_index

        # TODO: Uncomment when staking is implemented
        # contract_address, abi, function_name, params = self.generate_transaction()

        # contract = Web3Contract(client, contract_address, abi)
        # contract_function = getattr(contract.contract.functions, function_name)

        from_address = static_params_index[self.static_param_key]["account"]

        # TODO: Uncomment when staking is implemented
        # # Simulating unstaking transaction
        # logger.info("Simulating unstaking transaction...")
        # result = simulate_transaction(
        #     client,
        #     contract_function,
        #     params,
        #     from_address
        # )
        # if result:
        #     logger.info("Unstaking transaction simulation: successful")
        # else:
        #     logger.error("Unstaking transaction simulation: failed")
        #     return result

        # Initialize the LP contract
        self.lp_contract = Web3Contract(
            client,
            static_params_index[self.static_param_key]["lp_address"],
            static_params_index[self.static_param_key]["lp_abi"],
        )

        # TODO: Uncomment when staking is implemented
        # self.staking_contract = Web3Contract(
        #     client,
        #     static_params_index[self.static_param_key]["staking_address"],
        #     static_params_index[self.static_param_key]["staking_abi"]
        # )

        # Set the static parameters for withdrawing the LP
        self.liquidity = self.lp_contract.call(
            "balanceOf", [static_params_index[self.static_param_key]["account"]]
        )

        # TODO: Uncomment when staking is implemented
        # self.staked_amount = self.staking_contract.call(
        #     "balanceOf", [static_params_index[self.static_param_key]["account"]]
        # )

        self.tokenA = static_params_index[self.static_param_key]["tokenA"]
        self.tokenB = static_params_index[self.static_param_key]["tokenB"]
        self.stable = static_params_index[self.static_param_key]["stable"]

        self.amountAMin = 0
        self.amountBMin = 0

        self.to = static_params_index[self.static_param_key]["account"]

        self.deadline = int(time.time()) + 600

        # TODO: Uncomment when staking is implemented
        # if self.staked_amount != 0:
        #     logger.error("Failed to simulate the withdraw transaction from staking contract")
        #     return False

        # Approve Tokens
        token_address = static_params_index[self.static_param_key]["lp_address"]

        token_contract = Web3Contract(
            client,
            token_address,
            erc20_ABI,
        )

        logger.info("Simulating approve transaaction...")
        result = simulate_transaction(
            client,
            token_contract.contract.functions.approve,
            [
                token_address,
                self.liquidity,
            ],
            from_address,
        )
        if result:
            logger.info("Approve tranasction simulation: successful")
        else:
            logger.error("Approve transaction simulation: failed")

        logger.info("Simulating second approve transaction...")
        result = simulate_transaction(
            client,
            token_contract.contract.functions.approve,
            [
                self.address,
                self.liquidity,
            ],
            from_address,
        )
        if result:
            logger.info("Approve tranasction simulation: successful")
        else:
            logger.error("Approve transaction simulation: failed")

        # Simulating Liquidity Removal transaction
        contract_address, abi, function_name, params = self.generate_transaction2()
        contract = Web3Contract(
            client,
            contract_address,
            abi,
        )
        contract_function = getattr(contract.contract.functions, function_name)

        logger.info("Simulating liquidity remove transaction...")
        result = simulate_transaction(client, contract_function, params, from_address)
        if result:
            logger.info("liquidity remove transaction simulation: successful")
        else:
            logger.error("liquidity remove transaction simulation: failed")
            return result

        self.liquidity = self.lp_contract.call(
            "balanceOf", [static_params_index[self.static_param_key]["account"]]
        )
        if self.liquidity != 0:
            logger.error(
                f"Simulated transaction failed. Liquidity after simulation transaction: {self.liquidity}"
            )
            return False
        else:
            logger.info("Simulated transaction successful")
            return True

    def execute(self):
        from static_params_index import static_params_index

        # TODO: Uncomment when staking is implemented
        # try:
        #     contract_address, abi, function_name, params = self.generate_transaction()

        #     contract = Web3Contract(self.client, contract_address, abi)
        #     contract_function = getattr(contract.contract.functions, function_name)

        #     logger.info(f"Contract function: {contract_function}")

        #     tx_hash = build_and_send_transaction(
        #         static_params_index[self.static_param_key]["chain_id"],
        #         static_params_index[self.static_param_key]["vault_id"],
        #         self.client,
        #         contract_function,
        #         params,
        #         static_params_index[self.static_param_key]["account"],
        #     )
        # except Exception as e:
        #     logger.warning(f"Couldn't withdraw the staked position: {e}")
        #     return

        # self.set_params()

        try:
            contract_address, abi, function_name, params = self.generate_transaction2()
            contract_function = getattr(self.contract.contract.functions, function_name)

            logger.info(f"Contract function: {contract_function}")

            approve_token(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                static_params_index[self.static_param_key]["lp_address"],
                self.to,
                static_params_index[self.static_param_key]["lp_address"],
                self.liquidity,
            )

            approve_token(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                static_params_index[self.static_param_key]["lp_address"],
                self.to,
                self.liquidity,
                self.address,
            )

            return build_and_send_transaction(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                contract_function,
                params,
                static_params_index[self.static_param_key]["account"],
            )
        except Exception as e:
            logger.warning(f"Couldn't withdraw liquidity position: {e}")
            return
