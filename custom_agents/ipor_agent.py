from custom_agents.agent import Agent
from agent_info.ipor_agent_info import get_ABI, get_address, get_chain_name
from helpers.web3_utils.web3_contract import Web3Contract
from helpers.fordefi_transaction_helpers import build_and_send_transaction
from helpers.simulate_transaction import simulate_transaction
from helpers.log import get_logger

logger = get_logger(__name__)


class IPORsrUSDAgent(Agent):
    def __init__(self):
        self.static_param_key = "IPORsrUSDAgent"
        self.chain_name = get_chain_name()
        self.address = get_address()
        self.abi = get_ABI()
        super().__init__(self.chain_name, self.address, self.abi)
        self.balance = 0
        self.set_params()

    def set_params(self):
        from static_params_index import static_params_index

        try:
            account = static_params_index[self.static_param_key]["account"]
            self.balance = self.contract.call("balanceOf", [account])
            logger.info(f"Set balance for {account}: {self.balance}")
        except Exception as e:
            logger.error(
                f"Couldn't retrieve data for IPORWithdrawAgent initialization: {e}"
            )
            self.balance = 0

    def generate_transaction(self):
        from static_params_index import static_params_index

        try:
            contract_address = static_params_index[self.static_param_key][
                "withdraw_manager_address"
            ]
            abi = static_params_index[self.static_param_key]["withdraw_manager_abi"]
            function_name = "requestShares"
            params = [self.balance]
            logger.info(
                f"Generated transaction for requestShares with params: {params}"
            )
            return contract_address, abi, function_name, params
        except Exception as e:
            logger.error(f"Error generating transaction in IPORWithdrawAgent: {e}")
            return None, None, None, None

    def simulate_transaction(self, client):
        from static_params_index import static_params_index

        try:
            contract_address, abi, function_name, params = self.generate_transaction()
            if not all([contract_address, abi, function_name, params]):
                logger.error("Simulation aborted: missing transaction data.")
                return False
            from_address = static_params_index[self.static_param_key]["account"]
            contract = Web3Contract(client, contract_address, abi)
            contract_function = getattr(contract.contract.functions, function_name)
            logger.info(f"Simulating {function_name}({params}) from {from_address}")
            result = simulate_transaction(
                client, contract_function, params, from_address
            )
            if result:
                logger.info(f"Simulation successful for {function_name}")
            else:
                logger.error(f"Simulation failed for {function_name}")
            return result
        except Exception as e:
            logger.error(f"Error simulating transaction in IPORWithdrawAgent: {e}")
            return False

    def execute(self):
        from static_params_index import static_params_index

        try:
            contract_address, abi, function_name, params = self.generate_transaction()
            if not all([contract_address, abi, function_name, params]):
                logger.error("Execution aborted: missing transaction data.")
                return False
            from_address = static_params_index[self.static_param_key]["account"]
            contract = Web3Contract(self.client, contract_address, abi)
            contract_function = getattr(contract.contract.functions, function_name)
            logger.info(f"Executing {function_name}({params}) from {from_address}")
            tx_hash = build_and_send_transaction(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                contract_function,
                params,
                from_address,
            )
            if tx_hash:
                logger.info(
                    f"Execution successful for {function_name}, tx hash: {tx_hash}"
                )
                return tx_hash
            else:
                logger.error(f"Execution failed for {function_name}")
                return False
        except Exception as e:
            logger.error(f"Error executing transaction in IPORWithdrawAgent: {e}")
            return False
