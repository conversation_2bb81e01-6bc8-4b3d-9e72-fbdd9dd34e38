from custom_agents.agent import Agent
from agent_info.fx_stability_agent_info import get_ABI, get_address, get_chain_name
from helpers.web3_utils.web3_contract import Web3Contract
from helpers.fordefi_transaction_helpers import (
    build_and_send_transaction,
    approve_token,
)
from telegram_notifier import TelegramNotifier
from helpers.simulate_transaction import simulate_transaction

from helpers.log import get_logger

logger = get_logger(__name__)


class FxStabilityAgent(Agent):
    def __init__(self):
        self.static_param_key = "FxStabilityAgent"
        self.chain_name = get_chain_name()
        self.address = get_address()
        self.abi = get_ABI()
        super().__init__(self.chain_name, self.address, self.abi)
        self.set_params()

    def set_params(self):
        from static_params_index import static_params_index

        from_address = static_params_index[self.static_param_key]["account"]

        self.gauge_contract = Web3Contract(
            self.client,
            static_params_index[self.static_param_key]["gauge_address"],
            static_params_index[self.static_param_key]["gauge_abi"],
        )

        self.staked_amount = self.gauge_contract.call("balanceOf", [from_address])

        self.contract = Web3Contract(self.client, self.address, self.abi)

        self.liquidity = self.contract.call("balanceOf", [from_address])

        (self.redeem_request, _) = self.contract.call("redeemRequests", [from_address])

        if self.redeem_request == 0:
            self.shares = self.liquidity
        else:
            self.shares = max(0, self.liquidity - self.redeem_request)

    def generate_transaction(self):
        from static_params_index import static_params_index

        contract_address = static_params_index[self.static_param_key]["gauge_address"]
        abi = static_params_index[self.static_param_key]["gauge_abi"]
        function_name = "withdraw"
        params = [self.staked_amount]

        return contract_address, abi, function_name, params

    def generate_transaction2(self):
        contract_address = self.contract_address
        abi = self.abi
        function_name = "requestRedeem"
        params = [self.shares]

        return contract_address, abi, function_name, params

    def simulate_transaction(self, client):
        from static_params_index import static_params_index

        from_address = static_params_index[self.static_param_key]["account"]

        # Prepare first transaction
        contract_address, abi, function_name, params = self.generate_transaction()
        contract = Web3Contract(
            client,
            contract_address,
            abi,
        )
        contract_function = getattr(contract.contract.functions, function_name)

        # Approve transaction
        logger.info("Simulating approve transaction...")
        result = simulate_transaction(
            client,
            getattr(contract.contract.functions, "approve"),
            [
                static_params_index[self.static_param_key]["diamond_address"],
                self.staked_amount,
            ],
            from_address,
        )
        if result:
            logger.info("Approve transaction simulation: successful")
        else:
            logger.error("Approve transaction simulation: failed")
            return result

        # Simulate unstaking transaction
        logger.info("Simulating unstaking transaction...")
        result = simulate_transaction(client, contract_function, params, from_address)
        if result:
            logger.info("Unstaking transaction simulation: successful")
        else:
            logger.error("Unstaking transcation simulation: failed")
            return result

        # Confirm the expected state
        staked_amount = contract.call("balanceOf", [from_address])
        if staked_amount != 0:
            logger.error(
                f"Simulation failed. Couldn't unstake the position: {staked_amount}"
            )
            return False
        else:
            logger.info("Successfully simulated unstaking transaction")

        # Prepare second transaction
        contract_address, abi, function_name, params = self.generate_transaction2()
        contract = Web3Contract(
            client,
            contract_address,
            abi,
        )
        contract_function = getattr(contract.contract.functions, function_name)

        liquidity = contract.call("balanceOf", [from_address])

        # Simulate requesting for redemption transaction
        logger.info("Simulating redeem request transaction...")
        result = simulate_transaction(
            client, contract_function, [liquidity], from_address
        )
        if result:
            logger.info("Redeem request transaction simulation: successful")
        else:
            logger.error("Redeem request transcation simulation: failed")
            return result

        # Confirm the expected state
        amount, unlock_at = contract.call("redeemRequests", [from_address])
        if amount == 0:
            logger.error(f"Simulation failed. Didn't reach expected state: {amount}")
        else:
            logger.info("Simulation successful: Reached expected state")
            return True

    def execute(self):
        from static_params_index import static_params_index

        try:
            contract_address, abi, function_name, params = self.generate_transaction()
            contract = Web3Contract(self.client, contract_address, abi)
            contract_function = getattr(contract.contract.functions, function_name)
            logger.info(f"Contract function: {contract_function}")
            from_address = static_params_index[self.static_param_key]["account"]

            approve_token(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                self.contract_address,
                from_address,
                static_params_index[self.static_param_key]["diamond_address"],
            )

            build_and_send_transaction(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                contract_function,
                params,
                from_address,
            )
        except Exception as e:
            logger.error(f"Couldn't withdraw the staked position: {e}")
            return

        # Reset params for the next function
        self.set_params()

        try:
            contract_address, abi, function_name, params = self.generate_transaction2()
            contract = Web3Contract(self.client, contract_address, abi)
            contract_function = getattr(contract.contract.functions, function_name)
            logger.info(f"Contract funcitons: {contract_function}")

            tx_hash = build_and_send_transaction(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                contract_function,
                params,
                from_address,
            )
            TelegramNotifier.notify(
                f"Redeem request sent for {self.__class__.__name__}: {tx_hash}"
            )
            return tx_hash
        except Exception as e:
            logger.error(f"Couldn't withdraw liquidity: {e}")
            return
