import sys
import os

sys.path.append(os.getcwd())
import math
from custom_agents.agent import Agent
from helpers.web3_utils.web3_contract import Web3Contract
from agent_info.morpho_base_vault_agent_info import get_ABI, get_address, get_chain_name
from helpers.fordefi_transaction_helpers import build_and_send_transaction
from helpers.simulate_transaction import simulate_transaction

from helpers.log import get_logger

logger = get_logger(__name__)


class MorphoOpenEdenUSR(Agent):
    def __init__(self):
        self.static_param_key = "MorphoOpenEdenUSR"
        self.chain_name = get_chain_name()
        self.address = get_address()
        self.abi = get_ABI()
        super().__init__(self.chain_name, self.address, self.abi)
        self.set_params()

    def set_params(self):
        from static_params_index import static_params_index

        VIRTUAL_ASSETS = 1
        VIRTUAL_SHARES = 10**8
        UINT256MAX = 115792089237316195423570985008687907853269984665640564039457584007913129639935

        self.market_id = static_params_index[self.static_param_key]["market_id"]
        self.collateral_token = static_params_index[self.static_param_key][
            "collateral_token"
        ]
        self.loan_token = static_params_index[self.static_param_key]["loan_token"]
        self.oracle = static_params_index[self.static_param_key]["oracle"]
        self.lltv = static_params_index[self.static_param_key]["lltv"]
        self.irm = static_params_index[self.static_param_key]["irm"]
        self.idle_market_id = static_params_index[self.static_param_key][
            "idle_market_id"
        ]

        morpho_contract = Web3Contract(
            self.client,
            self.contract.call("MORPHO"),
            static_params_index[self.static_param_key]["MORPHO_ABI"],
        )
        self.liquidity = morpho_contract.call(
            "market", [static_params_index[self.static_param_key]["market_id"]]
        )

        self.new_market_allocations = {}

        position = morpho_contract.call("position", [self.market_id, self.address])
        supply_shares = position[0]

        market = morpho_contract.call("market", [self.market_id])

        market_last_updated = market[4]
        latest_block = self.client.eth.get_block("latest")

        elapsed = latest_block["timestamp"] - market_last_updated
        fee = market[5]

        market_params = morpho_contract.call("idToMarketParams", [self.market_id])

        irm_contract = Web3Contract(
            self.client, self.irm, static_params_index[self.static_param_key]["IRM_ABI"]
        )

        def w_taylor_compounded(x, n):
            first_term = x * n
            second_term = first_term * first_term // (2 * 10**18)
            third_term = second_term * first_term // (3 * 10**18)
            return first_term + second_term + third_term

        total_supply_assets = market[0]
        total_supply_shares = market[1]
        total_borrow_assets = market[2]
        if elapsed != 0 and total_borrow_assets != 0:
            borrow_rate = irm_contract.call(
                "borrowRateView", [tuple(market_params), tuple(market)]
            )
            interest = (
                total_borrow_assets * w_taylor_compounded(borrow_rate, elapsed)
            ) // 10**18
            interest = int(interest * 0.995)

            total_supply_assets = market[0] + interest
            total_supply_shares = market[1]
            total_borrow_assets = market[2] + interest

            if fee != 0:
                fee_amount = interest * 10**18 // fee
                fee_shares = (
                    fee_amount
                    * (total_supply_assets - fee_amount)
                    // total_supply_shares
                    + VIRTUAL_SHARES
                )
                total_supply_shares += fee_shares

        if total_supply_shares == 0:
            supply_assets = supply_shares * total_supply_assets // VIRTUAL_SHARES
        else:
            supply_assets = int(
                supply_shares * total_supply_assets // total_supply_shares
            )

        available_liquidity = total_supply_assets - total_borrow_assets

        if supply_assets > available_liquidity:
            # New allocation is set so that exactly (supply_assets - new_allocation) == available_liquidity is withdrawn
            self.new_market_allocations[self.market_id] = math.ceil(
                supply_assets - available_liquidity
            ) + (
                10 * 10**6
            )  # Add 10 USDC buffer
        else:
            self.new_market_allocations[self.market_id] = 0

        idle_market_id = static_params_index[self.static_param_key]["idle_market_id"]
        self.new_market_allocations[idle_market_id] = UINT256MAX

        self.new_withdraw_queue = []
        withdraw_queue_length = self.contract.call("withdrawQueueLength")

        for withdraw_idx in range(withdraw_queue_length):
            market_id = self.contract.call("withdrawQueue", [withdraw_idx])
            if market_id == self.market_id:
                self.new_withdraw_queue.append(withdraw_idx)
            else:
                continue

        for withdraw_idx in range(withdraw_queue_length):
            market_id = self.contract.call("withdrawQueue", [withdraw_idx])
            if market_id == self.market_id:
                continue
            else:
                self.new_withdraw_queue.append(withdraw_idx)

        self.should_submit_cap = 0 != self.contract.call("config", [self.market_id])[0]

    def generate_transaction(self):
        from static_params_index import static_params_index

        contract_address = self.contract_address
        abi = self.abi
        function_name = "reallocate"
        params = [
            [
                tuple(
                    [
                        tuple(
                            [
                                self.loan_token,
                                self.collateral_token,
                                self.oracle,
                                self.irm,
                                int(self.lltv),
                            ]
                        ),
                        int(self.new_market_allocations[self.market_id]),
                    ]
                ),
                tuple(
                    [
                        tuple(
                            [
                                self.loan_token,
                                static_params_index[self.static_param_key][
                                    "null_address"
                                ],
                                static_params_index[self.static_param_key][
                                    "null_address"
                                ],
                                static_params_index[self.static_param_key][
                                    "null_address"
                                ],
                                0,
                            ]
                        ),
                        self.new_market_allocations[self.idle_market_id],
                    ]
                ),
            ]
        ]

        return contract_address, abi, function_name, params

    def generate_transaction2(self):
        contract_address = self.contract_address
        abi = self.abi
        function_name = "updateWithdrawQueue"
        params = [self.new_withdraw_queue]

        return contract_address, abi, function_name, params

    def simulate_transaction(self, client):
        from static_params_index import static_params_index

        self.set_params()

        from_address = static_params_index[self.static_param_key]["account"]

        # Prepare first transaction
        contract_address, abi, function_name, params = self.generate_transaction()
        contract = Web3Contract(
            client,
            contract_address,
            abi,
        )
        contract_function = getattr(contract.contract.functions, function_name)

        # Simulate reallocate transaction
        logger.info("Simulating reallocate transaction...")
        result = simulate_transaction(client, contract_function, params, from_address)
        if result:
            logger.info("Reallocation transaction simulation: successful")
        else:
            logger.error("Reallocation transcation simulation: failed")
            return result

        # Prepare second transaction
        contract_address, abi, function_name, params = self.generate_transaction2()
        contract = Web3Contract(
            client,
            contract_address,
            abi,
        )
        contract_function = getattr(contract.contract.functions, function_name)

        # Simulate updateWithdrawQueue transaction
        logger.info("Simulating updateWithdrawQueue transaction...")
        result = simulate_transaction(client, contract_function, params, from_address)
        if result:
            logger.info("Withdraw queue update transaction simulation: successful")
        else:
            logger.error("Withdraw queue update transcation simulation: failed")
            return result

        return True

    def execute(self):
        from static_params_index import static_params_index

        try:
            contract_address, abi, function_name, params = self.generate_transaction()
            contract = Web3Contract(self.client, contract_address, abi)
            contract_function = getattr(contract.contract.functions, function_name)
            logger.info(f"Contract function: {contract_function}")
            from_address = static_params_index[self.static_param_key]["account"]

            tx_hash = build_and_send_transaction(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                contract_function,
                params,
                from_address,
            )

            logger.info(f"Transaction hash: {tx_hash}")
        except Exception as e:
            logger.error(f"Couldn't reallocate: {e}")
            return

        try:
            contract_address, abi, function_name, params = self.generate_transaction2()
            contract = Web3Contract(self.client, contract_address, abi)
            contract_function = getattr(contract.contract.functions, function_name)
            logger.info(f"Contract function: {contract_function}")
            from_address = static_params_index[self.static_param_key]["account"]

            tx_hash = build_and_send_transaction(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                contract_function,
                params,
                from_address,
            )

            logger.info(f"Transaction hash: {tx_hash}")
        except Exception as e:
            logger.error(f"Couldn't update withdraw queue: {e}")
            return
