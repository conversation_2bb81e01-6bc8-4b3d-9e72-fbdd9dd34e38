from custom_agents.agent import Agent
from agent_info.superform_agent_info import get_ABI, get_address, get_chain_name
from helpers.web3_utils.web3_contract import Web3Contract
from web3 import Web3
from helpers.fordefi_transaction_helpers import build_and_send_transaction, approve_nft
from helpers.simulate_transaction import simulate_transaction
import requests

from helpers.log import get_logger

logger = get_logger(__name__)


class SuperformEulerAgentUSDC(Agent):
    def __init__(self):
        self.static_param_key = "SuperformEulerAgentUSDC"
        self.chain_name = get_chain_name()
        self.address = get_address()
        self.abi = get_ABI()
        super().__init__(self.chain_name, self.address, self.abi)
        self.init_subgraph()
        self.set_params()

    def init_subgraph(self):
        from static_params_index import static_params_index

        self.subgraph_url = static_params_index[self.static_param_key]["subgraph_url"]

    def set_params(self):
        from static_params_index import static_params_index

        def fetch_from_gql(subgraph_url, query, variables=None):
            response = requests.post(
                subgraph_url, json={"query": query, "variables": variables}
            )
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Query failed with status code: {response.status_code}")
                logger.info(f"Response: {response.text}")
                return None

        user_address = static_params_index[self.static_param_key]["account"]

        superform_id_query = """
        query GetSuperformId($user: String!) {
            userSuperpositionBalances(where: { user: $user }) {
                superpositionsID
                balance
            }
        }
        """
        variables = {"user": user_address.lower()}
        superform_position = fetch_from_gql(
            self.subgraph_url, superform_id_query, variables
        )

        for position in superform_position["data"]["userSuperpositionBalances"]:
            superform_address_query = """
            query SuperformDetails($superformId: String!) {
                superform(id: $superformId) {
                    superformAddress
                }
            }
            """
            variables = {"superformId": position["superpositionsID"]}

            response = fetch_from_gql(
                self.subgraph_url, superform_address_query, variables
            )
            superform_address = response["data"]["superform"]["superformAddress"]

            if (
                superform_address
                == static_params_index[self.static_param_key]["superform_address"]
                and int(position["balance"]) != 0
            ):
                self.superform_id = position["superpositionsID"]
                self.balance = position["balance"]
                break

        self.superform_address = static_params_index[self.static_param_key][
            "superform_address"
        ]

        superform_contract = Web3Contract(
            self.client,
            self.superform_address,
            static_params_index[self.static_param_key]["superform_abi"],
        )

        self.output_amount = superform_contract.call(
            "previewRedeemFrom", [int(self.balance)]
        )

    def generate_transaction(self, balance_multiplier=1):
        from static_params_index import static_params_index

        contract_address = static_params_index[self.static_param_key][
            "superform_router_address"
        ]
        abi = static_params_index[self.static_param_key]["superform_router_abi"]
        function_name = "singleDirectSingleVaultWithdraw"
        params = tuple(
            (
                int(self.superform_id),
                int(int(self.balance) * balance_multiplier),
                int(self.output_amount * balance_multiplier),
                50,
                (
                    "0x",
                    "0x0000000000000000000000000000000000000000",
                    "0x0000000000000000000000000000000000000000",
                    1,
                    0,
                    0,
                ),
                "0x",
                False,
                False,
                static_params_index[self.static_param_key]["account"],
                static_params_index[self.static_param_key]["account"],
                "0x",
            ),
        )

        return contract_address, abi, function_name, tuple((params,))

    def simulate_transaction(self, client: Web3, balance_multiplier=1):
        from static_params_index import static_params_index

        from_address = static_params_index[self.static_param_key]["account"]

        contract_address, abi, function_name, params = self.generate_transaction(
            balance_multiplier
        )
        contract = Web3Contract(client, contract_address, abi)
        contract_function = getattr(contract.contract.functions, function_name)

        superposition_contract = Web3Contract(
            client,
            static_params_index[self.static_param_key]["superposition_address"],
            static_params_index[self.static_param_key]["superposition_abi"],
        )

        # Approve the contract
        logger.info("Simulating approve transaction...")
        result = simulate_transaction(
            client,
            getattr(superposition_contract.contract.functions, "setApprovalForAll"),
            [
                contract_address,
                True,
            ],
            from_address,
        )
        if result:
            logger.info("Approve transaction simulation: successful")
        else:
            logger.error("Approve transaction simulation: failed")
            return result

        # Build and send the transaction
        logger.info("Simulating withdrawal transaction...")
        result = simulate_transaction(client, contract_function, params, from_address)
        if result:
            logger.info("Withdrawal transaction simulation: successful")
        else:
            logger.error("Withdrawal transaction simulation: failed")

            if balance_multiplier >= 0.2:
                logger.info(
                    f"Retrying to simulate with balance multiplier {balance_multiplier-0.1}"
                )
                return self.simulate_transaction(client, balance_multiplier - 0.1)
            else:
                return False

        # Check the state after the transaction simulation
        superposition_contract = Web3Contract(
            client,
            static_params_index[self.static_param_key]["superposition_address"],
            static_params_index[self.static_param_key]["superposition_abi"],
        )

        superposition_balance = superposition_contract.call(
            "balanceOf", [from_address, int(self.superform_id)]
        )
        if superposition_balance == self.balance:
            logger.error(
                f"Simulated transaction failed. Superposition balance: {superposition_balance}"
            )
            return False
        else:
            logger.info("Simulated transaction successful: Reached expected state")
            return True

    def execute(self, balance_multiplier=1):
        from static_params_index import static_params_index

        contract_address, abi, function_name, params = self.generate_transaction(
            balance_multiplier
        )
        contract = Web3Contract(self.client, contract_address, abi)
        contract_function = getattr(contract.contract.functions, function_name)
        logger.info(f"Contract function: {contract_function}")
        from_address = static_params_index[self.static_param_key]["account"]

        superposition_contract = Web3Contract(
            self.client,
            static_params_index[self.static_param_key]["superposition_address"],
            static_params_index[self.static_param_key]["superposition_abi"],
        )
        approve_nft(
            static_params_index[self.static_param_key]["chain_id"],
            static_params_index[self.static_param_key]["vault_id"],
            self.client,
            superposition_contract,
            from_address,
            contract_address,
        )

        # Build and send the transaction
        try:
            tx_hash = build_and_send_transaction(
                static_params_index[self.static_param_key]["chain_id"],
                static_params_index[self.static_param_key]["vault_id"],
                self.client,
                contract_function,
                params,
                from_address,
            )
        except Exception as e:
            if balance_multiplier >= 0.2:
                logger.info(
                    f"Failed to execute the agent. Trying again with balance_multiplier {balance_multiplier-0.1}"
                )
                return self.execute(balance_multiplier - 0.1)
            else:
                logger.info(f"Failed to execute the agent: {e}")

        logger.info(f"Transaction sent with hash: {tx_hash}")
