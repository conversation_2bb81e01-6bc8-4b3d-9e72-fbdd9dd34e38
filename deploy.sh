export DOCKER_DEFAULT_PLATFORM=linux/amd64
aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 236840575805.dkr.ecr.eu-central-1.amazonaws.com

# Create and use a new builder instance
docker buildx create --use --name mybuilder
docker buildx inspect mybuilder --bootstrap

# Build the Docker image with the correct platform and format
docker buildx build --platform linux/amd64 --output "type=docker" -t hypernative_handler .

# Tag and push the Docker image
docker tag hypernative_handler:latest 236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_handler
docker push 236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_handler

# Update the AWS Lambda function with the new image URI
aws lambda update-function-code --function-name hypernative_handler --image-uri 236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_handler:latest

# Remove the builder instance
docker buildx rm mybuilder