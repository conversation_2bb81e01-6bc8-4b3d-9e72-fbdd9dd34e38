export DOCKER_DEFAULT_PLATFORM=linux/amd64
aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 236840575805.dkr.ecr.eu-central-1.amazonaws.com

# Create and use a new builder instance
docker buildx create --use --name mybuilder
docker buildx inspect mybuilder --bootstrap

# Build the Docker image with the correct platform and format
docker buildx build --platform linux/amd64 --output "type=docker" -t hypernative_simulator -f Dockerfile.simulation .

# Tag and push the Docker image
docker tag hypernative_simulator:latest 236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_simulator
docker push 236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_simulator

# Update the AWS Lambda function with the new image URI
aws lambda update-function-code --function-name hypernative_simulator --image-uri 236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_simulator:latest

# Remove the builder instance
docker buildx rm mybuilder

