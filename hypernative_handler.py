from helpers.sign_trigger_transaction import trigger_transaction_signing
from risk_index import risk_index
from custom_agents.agent import Agent
import json
import boto3
import hashlib
import time
from botocore.exceptions import ClientError

from dotenv import load_dotenv

load_dotenv()

from helpers.load_cloud_env import load_cloud_env

load_cloud_env()

from helpers.log import get_logger

logger = get_logger(__name__)

# Initialize AWS clients
lambda_client = boto3.client('lambda')
dynamodb = boto3.resource('dynamodb')

# Environment variables
IDEMPOTENCY_TABLE = "hypernative-idempotency"


def generate_idempotency_key(event):
    """Generate a unique idempotency key from the event."""
    # Use fordefi-transaction-id if available, otherwise hash the event body
    headers = event.get("headers", {})
    if "fordefi-transaction-id" in headers:
        return f"fordefi-{headers['fordefi-transaction-id']}"

    # For other events, create hash from body content
    body = event.get("body", "")
    event_hash = hashlib.sha256(body.encode()).hexdigest()[:16]
    return f"webhook-{event_hash}"


def check_and_set_idempotency(idempotency_key):
    """Check if event was already processed and mark as processing if not."""
    try:
        table = dynamodb.Table(IDEMPOTENCY_TABLE)

        # Try to put item with condition that it doesn't exist
        table.put_item(
            Item={
                'idempotency_key': idempotency_key,
                'timestamp': int(time.time()),
                'ttl': int(time.time()) + 86400  # 24 hour TTL
            },
            ConditionExpression='attribute_not_exists(idempotency_key)'
        )
        return True  # Successfully set, event is new
    except ClientError as e:
        if e.response['Error']['Code'] == 'ConditionalCheckFailedException':
            return False  # Event already processed
        raise  # Other error, re-raise


def invoke_async_processing(event):
    """Invoke the same Lambda function asynchronously to process agents."""
    try:
        # Create a modified event to indicate this is for async processing
        async_event = {
            **event,
            'async_processing': True
        }

        response = lambda_client.invoke(
            FunctionName='hypernative_handler',
            InvocationType='Event',  # Asynchronous invocation
            Payload=json.dumps(async_event)
        )
        logger.info(f"Async processing invoked: {response['StatusCode']}")
        return True
    except Exception as e:
        logger.error(f"Failed to invoke async processing: {e}")
        return False


def process_agents_sync(event):
    """Process the agent execution logic synchronously (for async invocations)."""
    try:
        # Extract the data from the event to detect the Custom Agent needed
        body = json.loads(event["body"])
        data = json.loads(body["data"])
        watchlists = data["watchlists"]

        # Process custom agents
        if "customAgents" in data:
            custom_agents = data["customAgents"]
            if custom_agents != []:
                for custom_agent in custom_agents:
                    agent_name = custom_agent["agentName"]
                    if agent_name in risk_index:
                        logger.info(f"Custom Agent detected: {agent_name}")
                        custom_agent_class = risk_index[agent_name]
                        if isinstance(custom_agent_class, list):
                            for agent_class in custom_agent_class:
                                try:
                                    custom_agent_instance: Agent = agent_class()
                                    logger.info(
                                        f"Executing Custom Agent: {custom_agent_instance.__class__.__name__}"
                                    )
                                    custom_agent_instance.execute()
                                except Exception as e:
                                    logger.error(
                                        f"Error executing agent {agent_class.__name__}: {e}"
                                    )
                        else:
                            custom_agent_instance: Agent = custom_agent_class()
                            logger.info(
                                f"Executing Custom Agent: {custom_agent_instance.__class__.__name__}"
                            )
                            custom_agent_instance.execute()

        # Process watchlists
        if watchlists != []:
            for watchlist in watchlists:
                watchlist_name = watchlist["name"]
                if watchlist_name in risk_index:
                    logger.info(f"Watchlist detected: {watchlist_name}")

                    assets = watchlist["assets"]
                    if assets != []:
                        for asset in assets:
                            risk_chain = asset["chain"]
                            if risk_chain in risk_index[watchlist_name]:
                                logger.info(f"Chain detected: {risk_chain}")
                                custom_agent_class = risk_index[watchlist_name][
                                    risk_chain
                                ]
                                if isinstance(custom_agent_class, list):
                                    for agent_class in custom_agent_class:
                                        try:
                                            custom_agent: Agent = agent_class()
                                            logger.info(
                                                f"Executing Custom Agent: {custom_agent.__class__.__name__}"
                                            )
                                            custom_agent.execute()
                                        except Exception as e:
                                            logger.error(
                                                f"Error executing agent {agent_class.__name__}: {e}"
                                            )
                                else:
                                    custom_agent: Agent = custom_agent_class()
                                    logger.info(
                                        f"Executing Custom Agent: {custom_agent.__class__.__name__}"
                                    )
                                    custom_agent.execute()
                                logger.info(
                                    f"Risk chain detected: {watchlist_name}"
                                )

        logger.info("Agent processing completed successfully")
        return True

    except Exception as e:
        logger.error(f"Error in agent processing: {e}")
        return False


def lambda_handler(event, context):
    """Lambda handler that processes webhooks fast or handles async agent processing."""
    start_time = time.time()

    try:
        # Check if this is an async processing invocation
        if event.get('async_processing'):
            logger.info("Processing agents asynchronously")
            process_agents_sync(event)
            processing_time = time.time() - start_time
            logger.info(f"Async agent processing completed in {processing_time:.2f}s")
            return {"statusCode": 200, "body": json.dumps("Async processing completed")}

        # Original webhook handling logic
        logger.info(f"Received webhook event at {start_time}")

        # Safely extract headers and body
        headers = event.get("headers", {})
        body = event.get("body")

        if not body:
            logger.warning("No body in webhook event")
            return {"statusCode": 200, "body": json.dumps("No-op: empty body")}

        # Generate idempotency key
        idempotency_key = generate_idempotency_key(event)
        logger.info(f"Processing event with idempotency key: {idempotency_key}")

        # Check if already processed
        if not check_and_set_idempotency(idempotency_key):
            logger.info(f"Event {idempotency_key} already processed, skipping")
            return {"statusCode": 200, "body": json.dumps("Event already processed")}

        # Handle ForDefi transaction signing synchronously (these are typically fast)
        if "fordefi-transaction-id" in headers:
            logger.info("Processing ForDefi transaction signing")
            trigger_transaction_signing(event)
            processing_time = time.time() - start_time
            logger.info(f"ForDefi processing completed in {processing_time:.2f}s")
            return {"statusCode": 200, "body": json.dumps("ForDefi transaction processed")}

        # For agent processing, invoke async processing
        if invoke_async_processing(event):
            processing_time = time.time() - start_time
            logger.info(f"Async processing invoked successfully in {processing_time:.2f}s")
            return {"statusCode": 200, "body": json.dumps("Webhook processing started")}
        else:
            # If async invocation fails, remove idempotency record to allow retry
            try:
                table = dynamodb.Table(IDEMPOTENCY_TABLE)
                table.delete_item(Key={'idempotency_key': idempotency_key})
            except Exception:
                pass  # Best effort cleanup

            return {"statusCode": 500, "body": json.dumps("Failed to start processing")}

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Error in webhook handler after {processing_time:.2f}s: {e}")
        return {"statusCode": 500, "body": json.dumps("Internal Server Error")}
