import requests
import os
from dotenv import load_dotenv

load_dotenv()

from helpers.load_cloud_env import load_cloud_env

load_cloud_env()

TELEGRAM_TOKEN = os.getenv("TELEGRAM_TOKEN")
CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

from helpers.log import get_logger

logger = get_logger(__name__)


class TelegramNotifier:
    @staticmethod
    def notify(text: str, error=None):
        if not TELEGRAM_TOKEN or not CHAT_ID:
            print("Telegram credentials not provided")
            return

        # Include error details if provided
        message = text
        if error:
            message = f"{text}\nError details: {error}"

        try:
            response = requests.post(
                f"https://api.telegram.org/bot{TELEGRAM_TOKEN}/sendMessage",
                json={"chat_id": CHAT_ID, "text": f"{message}"},
            )
            response.raise_for_status()
        except requests.exceptions.RequestException as error:
            logger.error(f"Error sending message to Telegram: {error}")
            return


if __name__ == "__main__":
    TelegramNotifier.notify("Hello, world!")
