from flask import Flask, request, jsonify
import requests
import os

app = Flask(__name__)


@app.route("/webhook", methods=["POST"])
def webhook():
    alert_data = request
    print(f"Received alert: {alert_data}")

    # Process the alert data here
    create_transaction(alert_data)

    return jsonify({"status": "success"}), 200


def create_transaction(alert_data):
    # Extract relevant data from the alert
    transaction_id = alert_data.headers.environ["HTTP_FORDEFI_TRANSACTION_ID"]
    # Get the bearer token from the environment
    bearer_token = os.getenv("FORDEFI_BEARER_TOKEN")

    # Call ForDefi API to create a transaction
    fordefi_url = (
        f"https://api.fordefi.com/api/v1/transactions/{transaction_id}/trigger-signing"
    )
    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    # Make a POST request to the ForDefi API
    response = requests.post(fordefi_url, headers=headers)

    # Check if the transaction was created successfully
    if response.status_code == 204:
        print("Transaction created successfully.")
    else:
        print(f"Failed to create transaction: {response.content}")


if __name__ == "__main__":
    app.run(port=5000)
