import json

address = "0xa195608C2306A26f727d5199D5A382a4508308DA"
ABI = json.loads(
    '[{"inputs":[{"internalType":"address","name":"superRegistry_","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"AddressInsufficientBalance","type":"error"},{"inputs":[],"name":"BLOCK_CHAIN_ID_OUT_OF_BOUNDS","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC1155InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"token","type":"address"}],"name":"FAILED_TO_EXECUTE_TXDATA","type":"error"},{"inputs":[],"name":"FailedInnerCall","type":"error"},{"inputs":[],"name":"INSUFFICIENT_ALLOWANCE_FOR_DEPOSIT","type":"error"},{"inputs":[],"name":"INSUFFICIENT_BALANCE","type":"error"},{"inputs":[],"name":"INSUFFICIENT_NATIVE_AMOUNT","type":"error"},{"inputs":[],"name":"INVALID_ACTION","type":"error"},{"inputs":[],"name":"INVALID_CHAIN_ID","type":"error"},{"inputs":[],"name":"INVALID_DEPOSIT_TOKEN","type":"error"},{"inputs":[],"name":"INVALID_SUPERFORMS_DATA","type":"error"},{"inputs":[],"name":"NO_TXDATA_PRESENT","type":"error"},{"inputs":[{"internalType":"address","name":"token","type":"address"}],"name":"SafeERC20FailedOperation","type":"error"},{"inputs":[],"name":"ZERO_ADDRESS","type":"error"},{"inputs":[],"name":"ZERO_AMOUNT","type":"error"},{"anonymous":false,"inputs":[],"name":"Completed","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"uint256","name":"payloadId","type":"uint256"},{"indexed":true,"internalType":"uint64","name":"dstChainId","type":"uint64"},{"indexed":false,"internalType":"uint256[]","name":"superformIds","type":"uint256[]"},{"indexed":false,"internalType":"uint256[]","name":"amountsIn","type":"uint256[]"},{"indexed":false,"internalType":"uint8[]","name":"bridgeIds","type":"uint8[]"},{"indexed":false,"internalType":"uint8[]","name":"ambIds","type":"uint8[]"}],"name":"CrossChainInitiatedDepositMulti","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"uint256","name":"payloadId","type":"uint256"},{"indexed":true,"internalType":"uint64","name":"dstChainId","type":"uint64"},{"indexed":false,"internalType":"uint256","name":"superformIds","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"amountIn","type":"uint256"},{"indexed":false,"internalType":"uint8","name":"bridgeId","type":"uint8"},{"indexed":false,"internalType":"uint8[]","name":"ambIds","type":"uint8[]"}],"name":"CrossChainInitiatedDepositSingle","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"uint256","name":"payloadId","type":"uint256"},{"indexed":true,"internalType":"uint64","name":"dstChainId","type":"uint64"},{"indexed":false,"internalType":"uint256[]","name":"superformIds","type":"uint256[]"},{"indexed":false,"internalType":"uint8[]","name":"ambIds","type":"uint8[]"}],"name":"CrossChainInitiatedWithdrawMulti","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"uint256","name":"payloadId","type":"uint256"},{"indexed":true,"internalType":"uint64","name":"dstChainId","type":"uint64"},{"indexed":false,"internalType":"uint256","name":"superformIds","type":"uint256"},{"indexed":false,"internalType":"uint8[]","name":"ambIds","type":"uint8[]"}],"name":"CrossChainInitiatedWithdrawSingle","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"token","type":"address"},{"indexed":true,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"RouterDustForwardedToPaymaster","type":"event"},{"inputs":[],"name":"CHAIN_ID","outputs":[{"internalType":"uint64","name":"","type":"uint64"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"token_","type":"address"}],"name":"forwardDustToPaymaster","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"components":[{"internalType":"uint8[][]","name":"ambIds","type":"uint8[][]"},{"internalType":"uint64[]","name":"dstChainIds","type":"uint64[]"},{"components":[{"internalType":"uint256[]","name":"superformIds","type":"uint256[]"},{"internalType":"uint256[]","name":"amounts","type":"uint256[]"},{"internalType":"uint256[]","name":"outputAmounts","type":"uint256[]"},{"internalType":"uint256[]","name":"maxSlippages","type":"uint256[]"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest[]","name":"liqRequests","type":"tuple[]"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool[]","name":"hasDstSwaps","type":"bool[]"},{"internalType":"bool[]","name":"retain4626s","type":"bool[]"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct MultiVaultSFData[]","name":"superformsData","type":"tuple[]"}],"internalType":"struct MultiDstMultiVaultStateReq","name":"req_","type":"tuple"}],"name":"multiDstMultiVaultDeposit","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"internalType":"uint8[][]","name":"ambIds","type":"uint8[][]"},{"internalType":"uint64[]","name":"dstChainIds","type":"uint64[]"},{"components":[{"internalType":"uint256[]","name":"superformIds","type":"uint256[]"},{"internalType":"uint256[]","name":"amounts","type":"uint256[]"},{"internalType":"uint256[]","name":"outputAmounts","type":"uint256[]"},{"internalType":"uint256[]","name":"maxSlippages","type":"uint256[]"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest[]","name":"liqRequests","type":"tuple[]"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool[]","name":"hasDstSwaps","type":"bool[]"},{"internalType":"bool[]","name":"retain4626s","type":"bool[]"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct MultiVaultSFData[]","name":"superformsData","type":"tuple[]"}],"internalType":"struct MultiDstMultiVaultStateReq","name":"req_","type":"tuple"}],"name":"multiDstMultiVaultWithdraw","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"internalType":"uint8[][]","name":"ambIds","type":"uint8[][]"},{"internalType":"uint64[]","name":"dstChainIds","type":"uint64[]"},{"components":[{"internalType":"uint256","name":"superformId","type":"uint256"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"outputAmount","type":"uint256"},{"internalType":"uint256","name":"maxSlippage","type":"uint256"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest","name":"liqRequest","type":"tuple"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool","name":"hasDstSwap","type":"bool"},{"internalType":"bool","name":"retain4626","type":"bool"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct SingleVaultSFData[]","name":"superformsData","type":"tuple[]"}],"internalType":"struct MultiDstSingleVaultStateReq","name":"req_","type":"tuple"}],"name":"multiDstSingleVaultDeposit","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"internalType":"uint8[][]","name":"ambIds","type":"uint8[][]"},{"internalType":"uint64[]","name":"dstChainIds","type":"uint64[]"},{"components":[{"internalType":"uint256","name":"superformId","type":"uint256"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"outputAmount","type":"uint256"},{"internalType":"uint256","name":"maxSlippage","type":"uint256"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest","name":"liqRequest","type":"tuple"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool","name":"hasDstSwap","type":"bool"},{"internalType":"bool","name":"retain4626","type":"bool"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct SingleVaultSFData[]","name":"superformsData","type":"tuple[]"}],"internalType":"struct MultiDstSingleVaultStateReq","name":"req_","type":"tuple"}],"name":"multiDstSingleVaultWithdraw","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"payloadIds","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"components":[{"components":[{"internalType":"uint256[]","name":"superformIds","type":"uint256[]"},{"internalType":"uint256[]","name":"amounts","type":"uint256[]"},{"internalType":"uint256[]","name":"outputAmounts","type":"uint256[]"},{"internalType":"uint256[]","name":"maxSlippages","type":"uint256[]"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest[]","name":"liqRequests","type":"tuple[]"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool[]","name":"hasDstSwaps","type":"bool[]"},{"internalType":"bool[]","name":"retain4626s","type":"bool[]"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct MultiVaultSFData","name":"superformData","type":"tuple"}],"internalType":"struct SingleDirectMultiVaultStateReq","name":"req_","type":"tuple"}],"name":"singleDirectMultiVaultDeposit","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"components":[{"internalType":"uint256[]","name":"superformIds","type":"uint256[]"},{"internalType":"uint256[]","name":"amounts","type":"uint256[]"},{"internalType":"uint256[]","name":"outputAmounts","type":"uint256[]"},{"internalType":"uint256[]","name":"maxSlippages","type":"uint256[]"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest[]","name":"liqRequests","type":"tuple[]"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool[]","name":"hasDstSwaps","type":"bool[]"},{"internalType":"bool[]","name":"retain4626s","type":"bool[]"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct MultiVaultSFData","name":"superformData","type":"tuple"}],"internalType":"struct SingleDirectMultiVaultStateReq","name":"req_","type":"tuple"}],"name":"singleDirectMultiVaultWithdraw","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"components":[{"internalType":"uint256","name":"superformId","type":"uint256"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"outputAmount","type":"uint256"},{"internalType":"uint256","name":"maxSlippage","type":"uint256"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest","name":"liqRequest","type":"tuple"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool","name":"hasDstSwap","type":"bool"},{"internalType":"bool","name":"retain4626","type":"bool"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct SingleVaultSFData","name":"superformData","type":"tuple"}],"internalType":"struct SingleDirectSingleVaultStateReq","name":"req_","type":"tuple"}],"name":"singleDirectSingleVaultDeposit","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"components":[{"internalType":"uint256","name":"superformId","type":"uint256"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"outputAmount","type":"uint256"},{"internalType":"uint256","name":"maxSlippage","type":"uint256"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest","name":"liqRequest","type":"tuple"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool","name":"hasDstSwap","type":"bool"},{"internalType":"bool","name":"retain4626","type":"bool"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct SingleVaultSFData","name":"superformData","type":"tuple"}],"internalType":"struct SingleDirectSingleVaultStateReq","name":"req_","type":"tuple"}],"name":"singleDirectSingleVaultWithdraw","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"internalType":"uint8[]","name":"ambIds","type":"uint8[]"},{"internalType":"uint64","name":"dstChainId","type":"uint64"},{"components":[{"internalType":"uint256[]","name":"superformIds","type":"uint256[]"},{"internalType":"uint256[]","name":"amounts","type":"uint256[]"},{"internalType":"uint256[]","name":"outputAmounts","type":"uint256[]"},{"internalType":"uint256[]","name":"maxSlippages","type":"uint256[]"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest[]","name":"liqRequests","type":"tuple[]"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool[]","name":"hasDstSwaps","type":"bool[]"},{"internalType":"bool[]","name":"retain4626s","type":"bool[]"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct MultiVaultSFData","name":"superformsData","type":"tuple"}],"internalType":"struct SingleXChainMultiVaultStateReq","name":"req_","type":"tuple"}],"name":"singleXChainMultiVaultDeposit","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"internalType":"uint8[]","name":"ambIds","type":"uint8[]"},{"internalType":"uint64","name":"dstChainId","type":"uint64"},{"components":[{"internalType":"uint256[]","name":"superformIds","type":"uint256[]"},{"internalType":"uint256[]","name":"amounts","type":"uint256[]"},{"internalType":"uint256[]","name":"outputAmounts","type":"uint256[]"},{"internalType":"uint256[]","name":"maxSlippages","type":"uint256[]"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest[]","name":"liqRequests","type":"tuple[]"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool[]","name":"hasDstSwaps","type":"bool[]"},{"internalType":"bool[]","name":"retain4626s","type":"bool[]"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct MultiVaultSFData","name":"superformsData","type":"tuple"}],"internalType":"struct SingleXChainMultiVaultStateReq","name":"req_","type":"tuple"}],"name":"singleXChainMultiVaultWithdraw","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"internalType":"uint8[]","name":"ambIds","type":"uint8[]"},{"internalType":"uint64","name":"dstChainId","type":"uint64"},{"components":[{"internalType":"uint256","name":"superformId","type":"uint256"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"outputAmount","type":"uint256"},{"internalType":"uint256","name":"maxSlippage","type":"uint256"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest","name":"liqRequest","type":"tuple"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool","name":"hasDstSwap","type":"bool"},{"internalType":"bool","name":"retain4626","type":"bool"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct SingleVaultSFData","name":"superformData","type":"tuple"}],"internalType":"struct SingleXChainSingleVaultStateReq","name":"req_","type":"tuple"}],"name":"singleXChainSingleVaultDeposit","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[{"components":[{"internalType":"uint8[]","name":"ambIds","type":"uint8[]"},{"internalType":"uint64","name":"dstChainId","type":"uint64"},{"components":[{"internalType":"uint256","name":"superformId","type":"uint256"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"outputAmount","type":"uint256"},{"internalType":"uint256","name":"maxSlippage","type":"uint256"},{"components":[{"internalType":"bytes","name":"txData","type":"bytes"},{"internalType":"address","name":"token","type":"address"},{"internalType":"address","name":"interimToken","type":"address"},{"internalType":"uint8","name":"bridgeId","type":"uint8"},{"internalType":"uint64","name":"liqDstChainId","type":"uint64"},{"internalType":"uint256","name":"nativeAmount","type":"uint256"}],"internalType":"struct LiqRequest","name":"liqRequest","type":"tuple"},{"internalType":"bytes","name":"permit2data","type":"bytes"},{"internalType":"bool","name":"hasDstSwap","type":"bool"},{"internalType":"bool","name":"retain4626","type":"bool"},{"internalType":"address","name":"receiverAddress","type":"address"},{"internalType":"address","name":"receiverAddressSP","type":"address"},{"internalType":"bytes","name":"extraFormData","type":"bytes"}],"internalType":"struct SingleVaultSFData","name":"superformData","type":"tuple"}],"internalType":"struct SingleXChainSingleVaultStateReq","name":"req_","type":"tuple"}],"name":"singleXChainSingleVaultWithdraw","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"superRegistry","outputs":[{"internalType":"contract ISuperRegistry","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"stateMutability":"payable","type":"receive"}]'
)

chain_name = "ETHEREUM"


def get_address():
    return address


def get_ABI():
    return ABI


def get_chain_name():
    return chain_name
