from custom_agents.ipor_agent import IPORsrUSDAgent
from custom_agents.dtrinity_agent import DTrinityAgent
from custom_agents.morpho_mainnet_usdc_reactor_vault_fxsave_agent import (
    MorphoUSDCReactorFxSave,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_ptfxsave_agent import (
    MorphoUSDCReactorPTfxSave,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_ptlvlusd_agent import (
    MorphoUSDCReactorPTlvlUSD,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_rlp_agent import (
    MorphoUSDCReactorRLP,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_wstusr_agent import (
    MorphoUSDCReactorwstUSR,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_ptrlp_agent import (
    MorphoUSDCReactorPTRLP,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_srusd_agent import (
    MorphoUSDCReactorsrUSD,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_yusd_agent import (
    MorphoUSDCReactoryUSD,
)

# Dictionary containing custom agent classes for each risk.
risk_index = {
    "dTrinity": {"sonic": DTrinityAgent},
    "IPOR": {"ethereum": IPORsrUSDAgent},
    "fx protocol": {"ethereum": [MorphoUSDCReactorFxSave, MorphoUSDCReactorPTfxSave]},
    "Level Money": {"ethereum": MorphoUSDCReactorPTlvlUSD},
    "Resolv Protocol": {
        "ethereum": [
            MorphoUSDCReactorRLP,
            MorphoUSDCReactorwstUSR,
            MorphoUSDCReactorPTRLP,
        ]
    },
    "Reservoir": {"ethereum": MorphoUSDCReactorsrUSD},
    "YieldFi": {"ethereum": MorphoUSDCReactoryUSD},
}
