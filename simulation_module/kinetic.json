{"id": "55372d0e-9d91-4726-828c-f64d36fc50c4", "created_at": "2025-05-01T18:36:48.139000Z", "modified_at": "2025-05-01T18:36:48.139000Z", "managed_transaction_data": {"created_by": {"id": "21fd6590-eadf-4617-b159-770d084bd57b", "user_type": "person", "name": "<EMAIL>", "email": "<EMAIL>", "state": "active", "role": "admin"}, "policy_match": {"is_default": false, "rule_id": "fccfeaf0-4efd-4022-b074-9dc8a79df8a8", "rule_name": "[KY] Allow contract interaction: Vaults <-> Whitelisted Contracts - 1/x", "action_type": "allow"}, "signer_type": "api_signer", "risks": [], "vault": {"id": "c1b26c31-db73-4f6d-8e74-eb679f344662", "name": "[KY] Flare deal", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682", "state": "active", "type": "evm"}, "has_current_user_vault_permissions": true, "push_mode": "auto", "sign_mode": "triggered"}, "signatures": [], "note": "", "spam_state": "unset", "direction": "outgoing", "signed_externally": false, "state": "waiting_for_signing_trigger", "state_changes": [{"changed_at": "2025-05-01T18:36:48.139000Z", "asset_prices": [], "prices": {"token_prices": []}, "new_state": "waiting_for_approval"}, {"changed_at": "2025-05-01T18:36:48.459000Z", "asset_prices": [], "prices": {"token_prices": []}, "previous_state": "waiting_for_approval", "new_state": "waiting_for_signing_trigger"}], "type": "evm_transaction", "evm_transaction_type_details": {"type": "contract_call"}, "chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}, "from": {"vault": {"id": "c1b26c31-db73-4f6d-8e74-eb679f344662", "name": "[KY] Flare deal", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682", "state": "active", "type": "evm"}, "explorer_url": "https://flarescan.com/address/0xc848B643D19bEEa82C739264080cDc8A751A6682", "type": "evm", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682"}, "to": {"explorer_url": "https://flarescan.com/address/******************************************", "contact": {"id": "c79c3955-7cd2-47d7-ae5a-525082667611", "name": "Kinetic: Lending USDT0", "address_ref": {"chain_type": "evm", "address": "******************************************", "chains": [{"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}]}}, "type": "evm", "address": "******************************************", "contract": {"name": "Lending USDT0", "dapp": {"id": "38e01d05-4411-5b54-b762-59db477b035e", "name": "Kinetic Market", "url": "https://app.kinetic.market/stake", "logo_url": "https://logos.fordefi.com/dapps/38e01d05-4411-5b54-b762-59db477b035e.png"}, "is_verified": true, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "Lending USDT0", "symbol": "kUSDT0", "type": "erc20", "decimals": 8}}}, "value": "0", "data": "hSoS4///////////////////////////////////////////", "hex_data": "0x852a12e3ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "parsed_data": {"method": "redeemUnderlying", "method_arguments": [{"name": "unkparam_0", "type": "uint256", "value": "115792089237316195423570985008687907853269984665640564039457584007913129639935"}]}, "expected_result": {"reversion": {"state": "not_reverted"}, "gas_debit": {"gas_used": "251601", "gas_price": "171976388032", "total_fee": "43269431205239232", "fiat_price": {"price": "2", "price_float": "1.980984", "fiat_currency": {"currency_symbol": "usd", "decimals": 2}}, "priced_asset": {"type": "asset_price", "asset_identifier": {"type": "evm", "details": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}, "type": "native"}}, "asset_info": {"id": "bf90a9f4-989b-4290-933b-e7b59b95d30c", "asset_identifier": {"type": "evm", "details": {"type": "native", "chain": "evm_flare_mainnet"}, "chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}}, "name": "Flare", "symbol": "FLR", "decimals": 18, "verified": true, "metadata_uri": "", "is_spam": false}, "price": {"price": "2", "price_float": "1.980984", "fiat_currency": {"currency_symbol": "usd", "decimals": 2}}}}, "effects": {"balance_changes": [{"priced_asset": {"type": "asset_price", "asset_identifier": {"type": "evm", "details": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}, "type": "erc20", "token": {"explorer_url": "https://flarescan.com/address/******************************************", "contact": {"id": "c79c3955-7cd2-47d7-ae5a-525082667611", "name": "Kinetic: Lending USDT0", "address_ref": {"chain_type": "evm", "address": "******************************************", "chains": [{"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}]}}, "type": "evm", "address": "******************************************", "contract": {"name": "Lending USDT0", "dapp": {"id": "38e01d05-4411-5b54-b762-59db477b035e", "name": "Kinetic Market", "url": "https://app.kinetic.market/stake", "logo_url": "https://logos.fordefi.com/dapps/38e01d05-4411-5b54-b762-59db477b035e.png"}, "is_verified": true, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "Lending USDT0", "symbol": "kUSDT0", "type": "erc20", "decimals": 8}}}}}, "asset_info": {"id": "1105d7f7-6cde-4e77-b9e8-aeba4c395cc7", "asset_identifier": {"type": "evm", "details": {"type": "erc20", "token": {"chain": "evm_flare_mainnet", "hex_repr": "******************************************"}}, "chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}}, "name": "Lending USDT0", "symbol": "kUSDT0", "decimals": 8, "verified": true, "metadata_uri": "", "is_spam": false, "explorer_url": "https://flarescan.com/address/******************************************"}, "price": {"price": "2", "price_float": "2.0009837546604306", "fiat_currency": {"currency_symbol": "usd", "decimals": 2}}}, "diff": "-1254423885822888", "type": "erc20", "address": {"vault": {"id": "c1b26c31-db73-4f6d-8e74-eb679f344662", "name": "[KY] Flare deal", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682", "state": "active", "type": "evm"}, "explorer_url": "https://flarescan.com/address/0xc848B643D19bEEa82C739264080cDc8A751A6682", "type": "evm", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682"}, "owner": {"vault": {"id": "c1b26c31-db73-4f6d-8e74-eb679f344662", "name": "[KY] Flare deal", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682", "state": "active", "type": "evm"}, "explorer_url": "https://flarescan.com/address/0xc848B643D19bEEa82C739264080cDc8A751A6682", "type": "evm", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682"}, "price": {"price": "2", "price_float": "2.0009837546604306", "fiat_currency": {"currency_symbol": "usd", "decimals": 2}}, "token_contract": {"name": "Lending USDT0", "dapp": {"id": "38e01d05-4411-5b54-b762-59db477b035e", "name": "Kinetic Market", "url": "https://app.kinetic.market/stake", "logo_url": "https://logos.fordefi.com/dapps/38e01d05-4411-5b54-b762-59db477b035e.png"}, "is_verified": true, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "Lending USDT0", "symbol": "kUSDT0", "type": "erc20", "decimals": 8}}}, {"priced_asset": {"type": "asset_price", "asset_identifier": {"type": "evm", "details": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}, "type": "erc20", "token": {"explorer_url": "https://flarescan.com/address/******************************************", "contact": {"id": "feba8f5b-1fbf-4c42-89cd-d6f36e4bc119", "name": "USDT0: TetherTokenOFTExtension", "address_ref": {"chain_type": "evm", "address": "******************************************", "chains": [{"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}]}}, "type": "evm", "address": "******************************************", "contract": {"name": "USD₮0", "is_verified": false, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "USD₮0", "symbol": "USD₮0", "type": "erc20", "decimals": 6, "logo_url": "https://logos.fordefi.com/tokens/14/******************************************.png"}}}}}, "asset_info": {"id": "92187e29-d61a-451c-95c3-5954284eba5b", "asset_identifier": {"type": "evm", "details": {"type": "erc20", "token": {"chain": "evm_flare_mainnet", "hex_repr": "******************************************"}}, "chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}}, "name": "USD₮0", "symbol": "USD₮0", "decimals": 6, "verified": false, "metadata_uri": "", "is_spam": false, "explorer_url": "https://flarescan.com/address/******************************************"}, "price": {"price": "100", "price_float": "100", "fiat_currency": {"currency_symbol": "usd", "decimals": 2}}}, "diff": "250885248539", "type": "erc20", "address": {"vault": {"id": "c1b26c31-db73-4f6d-8e74-eb679f344662", "name": "[KY] Flare deal", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682", "state": "active", "type": "evm"}, "explorer_url": "https://flarescan.com/address/0xc848B643D19bEEa82C739264080cDc8A751A6682", "type": "evm", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682"}, "owner": {"vault": {"id": "c1b26c31-db73-4f6d-8e74-eb679f344662", "name": "[KY] Flare deal", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682", "state": "active", "type": "evm"}, "explorer_url": "https://flarescan.com/address/0xc848B643D19bEEa82C739264080cDc8A751A6682", "type": "evm", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682"}, "price": {"price": "100", "price_float": "100", "fiat_currency": {"currency_symbol": "usd", "decimals": 2}}, "token_contract": {"name": "USD₮0", "is_verified": false, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "USD₮0", "symbol": "USD₮0", "type": "erc20", "decimals": 6, "logo_url": "https://logos.fordefi.com/tokens/14/******************************************.png"}}}], "transfers": [{"priced_asset": {"type": "asset_price", "asset_identifier": {"type": "evm", "details": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}, "type": "erc20", "token": {"explorer_url": "https://flarescan.com/address/******************************************", "contact": {"id": "feba8f5b-1fbf-4c42-89cd-d6f36e4bc119", "name": "USDT0: TetherTokenOFTExtension", "address_ref": {"chain_type": "evm", "address": "******************************************", "chains": [{"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}]}}, "type": "evm", "address": "******************************************", "contract": {"name": "USD₮0", "is_verified": false, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "USD₮0", "symbol": "USD₮0", "type": "erc20", "decimals": 6, "logo_url": "https://logos.fordefi.com/tokens/14/******************************************.png"}}}}}, "asset_info": {"id": "92187e29-d61a-451c-95c3-5954284eba5b", "asset_identifier": {"type": "evm", "details": {"type": "erc20", "token": {"chain": "evm_flare_mainnet", "hex_repr": "******************************************"}}, "chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}}, "name": "USD₮0", "symbol": "USD₮0", "decimals": 6, "verified": false, "metadata_uri": "", "is_spam": false, "explorer_url": "https://flarescan.com/address/******************************************"}, "price": {"price": "100", "price_float": "100", "fiat_currency": {"currency_symbol": "usd", "decimals": 2}}}, "amount": "250885248539", "type": "erc20", "from": {"explorer_url": "https://flarescan.com/address/******************************************", "contact": {"id": "c79c3955-7cd2-47d7-ae5a-525082667611", "name": "Kinetic: Lending USDT0", "address_ref": {"chain_type": "evm", "address": "******************************************", "chains": [{"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}]}}, "type": "evm", "address": "******************************************", "contract": {"name": "Lending USDT0", "dapp": {"id": "38e01d05-4411-5b54-b762-59db477b035e", "name": "Kinetic Market", "url": "https://app.kinetic.market/stake", "logo_url": "https://logos.fordefi.com/dapps/38e01d05-4411-5b54-b762-59db477b035e.png"}, "is_verified": true, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "Lending USDT0", "symbol": "kUSDT0", "type": "erc20", "decimals": 8}}}, "to": {"vault": {"id": "c1b26c31-db73-4f6d-8e74-eb679f344662", "name": "[KY] Flare deal", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682", "state": "active", "type": "evm"}, "explorer_url": "https://flarescan.com/address/0xc848B643D19bEEa82C739264080cDc8A751A6682", "type": "evm", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682"}, "price": {"price": "100", "price_float": "100", "fiat_currency": {"currency_symbol": "usd", "decimals": 2}}, "token_contract": {"name": "USD₮0", "is_verified": false, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "USD₮0", "symbol": "USD₮0", "type": "erc20", "decimals": 6, "logo_url": "https://logos.fordefi.com/tokens/14/******************************************.png"}}}, {"priced_asset": {"type": "asset_price", "asset_identifier": {"type": "evm", "details": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}, "type": "erc20", "token": {"explorer_url": "https://flarescan.com/address/******************************************", "contact": {"id": "c79c3955-7cd2-47d7-ae5a-525082667611", "name": "Kinetic: Lending USDT0", "address_ref": {"chain_type": "evm", "address": "******************************************", "chains": [{"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}]}}, "type": "evm", "address": "******************************************", "contract": {"name": "Lending USDT0", "dapp": {"id": "38e01d05-4411-5b54-b762-59db477b035e", "name": "Kinetic Market", "url": "https://app.kinetic.market/stake", "logo_url": "https://logos.fordefi.com/dapps/38e01d05-4411-5b54-b762-59db477b035e.png"}, "is_verified": true, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "Lending USDT0", "symbol": "kUSDT0", "type": "erc20", "decimals": 8}}}}}, "asset_info": {"id": "1105d7f7-6cde-4e77-b9e8-aeba4c395cc7", "asset_identifier": {"type": "evm", "details": {"type": "erc20", "token": {"chain": "evm_flare_mainnet", "hex_repr": "******************************************"}}, "chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet", "name": "Flare", "native_currency_symbol": "FLR", "native_currency_name": "Flare", "blockchain_explorer": {"transaction_url": "https://flarescan.com/txs/", "address_url": "https://flarescan.com/address/", "root_url": "https://flarescan.com/", "transaction_format_url": "https://flarescan.com/tx/%s", "address_format_url": "https://flarescan.com/address/%s", "asset_format_url": "https://flarescan.com/address/%s"}, "logo_url": "https://logos.fordefi.com/chains/evm/default/14.svg", "is_testnet": false, "is_enabled": true, "gas_type": "dynamic", "supports_secure_node": false, "source": "default"}}, "name": "Lending USDT0", "symbol": "kUSDT0", "decimals": 8, "verified": true, "metadata_uri": "", "is_spam": false, "explorer_url": "https://flarescan.com/address/******************************************"}, "price": {"price": "2", "price_float": "2.0009837546604306", "fiat_currency": {"currency_symbol": "usd", "decimals": 2}}}, "amount": "1254423885822888", "type": "erc20", "from": {"vault": {"id": "c1b26c31-db73-4f6d-8e74-eb679f344662", "name": "[KY] Flare deal", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682", "state": "active", "type": "evm"}, "explorer_url": "https://flarescan.com/address/0xc848B643D19bEEa82C739264080cDc8A751A6682", "type": "evm", "address": "0xc848B643D19bEEa82C739264080cDc8A751A6682"}, "to": {"explorer_url": "https://flarescan.com/address/******************************************", "contact": {"id": "c79c3955-7cd2-47d7-ae5a-525082667611", "name": "Kinetic: Lending USDT0", "address_ref": {"chain_type": "evm", "address": "******************************************", "chains": [{"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}]}}, "type": "evm", "address": "******************************************", "contract": {"name": "Lending USDT0", "dapp": {"id": "38e01d05-4411-5b54-b762-59db477b035e", "name": "Kinetic Market", "url": "https://app.kinetic.market/stake", "logo_url": "https://logos.fordefi.com/dapps/38e01d05-4411-5b54-b762-59db477b035e.png"}, "is_verified": true, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "Lending USDT0", "symbol": "kUSDT0", "type": "erc20", "decimals": 8}}}, "price": {"price": "2", "price_float": "2.0009837546604306", "fiat_currency": {"currency_symbol": "usd", "decimals": 2}}, "token_contract": {"name": "Lending USDT0", "dapp": {"id": "38e01d05-4411-5b54-b762-59db477b035e", "name": "Kinetic Market", "url": "https://app.kinetic.market/stake", "logo_url": "https://logos.fordefi.com/dapps/38e01d05-4411-5b54-b762-59db477b035e.png"}, "is_verified": true, "token": {"address": {"chain": {"chain_type": "evm", "named_chain_id": "flare_mainnet", "chain_id": 14, "unique_id": "evm_flare_mainnet"}, "hex_repr": "******************************************"}, "name": "Lending USDT0", "symbol": "kUSDT0", "type": "erc20", "decimals": 8}}}], "allowances": [], "bridge": [], "contract_deployments": []}}, "simulation_status_result": {"simulation_status": "success", "details": "not reverted"}, "gas_submitted": {"max_priority_fee_per_gas": "141976388032", "max_fee_per_gas": "171976388032", "priority": "medium", "limit": "397083", "type": "dynamic"}, "is_cancelation": false, "is_acceleration": false, "use_secure_node": false}