import sys
import os
import subprocess
import socket
import time
import json

sys.path.append(os.getcwd())
from telegram_notifier import TelegramNotifier
from web3 import Web3
from helpers.simulate_transaction import (
    simulate_transaction_inner,
)
from helpers.get_transactions import (
    get_fordefi_transactions_response,
    get_transaction_details,
)
from helpers.web3_utils.web3_connector import Web3Connector
from helpers.provider_url import PROVIDER_URL
from helpers.log import get_logger
from static_params_index import static_params_index
from custom_agents.dtrinity_agent import DTrinityAgent
from custom_agents.ipor_agent import IPORsrUSDAgent
from custom_agents.morpho_mainnet_usdc_reactor_vault_fxsave_agent import (
    MorphoUSDCReactorFxSave,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_ptfxsave_agent import (
    MorphoUSDCReactorPTfxSave,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_ptlvlusd_agent import (
    MorphoUSDCReactorPTlvlUSD,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_rlp_agent import (
    MorphoUSDCReactorRLP,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_wstusr_agent import (
    MorphoUSDCReactorwstUSR,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_ptrlp_agent import (
    MorphoUSDCReactorPTRLP,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_srusd_agent import (
    MorphoUSDCReactorsrUSD,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_yusd_agent import (
    MorphoUSDCReactoryUSD,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_susds_agent import (
    MorphoUSDCReactorSUSDS,
)
from custom_agents.morpho_mainnet_usdc_reactor_vault_usr_agent import (
    MorphoUSDCReactorUSR,
)
from custom_agents.agent import Agent
from helpers.load_cloud_env import load_cloud_env
from dotenv import load_dotenv

load_dotenv()


load_cloud_env()


logger = get_logger(__name__)


def get_free_port():
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(("", 0))
        return s.getsockname()[1]


ANVIL_PORT = get_free_port()

# List of agents to run simulations on
LIST_OF_AGENTS: list[Agent] = [
    IPORsrUSDAgent,
    DTrinityAgent,
    MorphoUSDCReactorFxSave,
    MorphoUSDCReactorPTfxSave,
    MorphoUSDCReactorPTlvlUSD,
    MorphoUSDCReactorRLP,
    MorphoUSDCReactorwstUSR,
    MorphoUSDCReactorPTRLP,
    MorphoUSDCReactorsrUSD,
    MorphoUSDCReactoryUSD,
    MorphoUSDCReactorSUSDS,
    MorphoUSDCReactorUSR,
]


def start_anvil(provider_url, chain_id):
    logger.info("Starting Anvil fork...")
    anvil_command = [
        "anvil",
        "--fork-url",
        provider_url,
        "--chain-id",
        str(chain_id),
        "-p",
        str(ANVIL_PORT),
        "--auto-impersonate",
    ]

    anvil_process = subprocess.Popen(
        anvil_command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
    )
    time.sleep(15)  # Wait for Anvil to start
    if anvil_process.poll() is not None:
        _, stderr = anvil_process.communicate()
        logger.info(f"Anvil failed to start. Error: {stderr}")
        exit(1)
    logger.info("Anvil started successfully.")
    return anvil_process


def stop_process(process):
    logger.info("Stopping Anvil fork...")
    process.terminate()
    logger.info("Anvil stopped successfully.")


def get_web3_client_with_retry(max_retries=3, initial_delay=1) -> Web3:
    delay = initial_delay
    for attempt in range(max_retries):
        client = Web3Connector(f"http://127.0.0.1:{ANVIL_PORT}").get_client()
        if not client.is_connected():
            logger.info(f"Attempt {attempt + 1} failed. Retrying in {delay} seconds...")
            time.sleep(delay)
            delay *= 2
        else:
            return client

    logger.error(f"Failed to connect to Ganache after {max_retries} attempts.")
    raise ConnectionError("Failed to connect to Ganache")


def fund_wallet(client: Web3, address, amount_in_eth=10):
    """
    Fund a wallet with ETH in the Anvil simulation.

    Args:
        client: Web3 client instance
        address: Address to fund
        amount_in_eth: Amount of ETH to send (default: 10)
    """
    # Convert ETH to Wei
    amount_in_wei = Web3.to_wei(amount_in_eth, "ether")

    # Get a rich account from Anvil (index 0 is usually funded with 10000 ETH)
    rich_account = client.eth.accounts[0]

    # Check rich account balance
    rich_balance = client.eth.get_balance(rich_account)
    logger.info(
        f"Rich account {rich_account} balance: {Web3.from_wei(rich_balance, 'ether')} ETH"
    )

    # Check target account balance before funding
    target_balance_before = client.eth.get_balance(address)
    logger.info(
        f"Target account {address} balance before funding: {Web3.from_wei(target_balance_before, 'ether')} ETH"
    )

    # Send transaction
    tx_hash = client.eth.send_transaction(
        {"from": rich_account, "to": address, "value": amount_in_wei}
    )

    # Wait for transaction to be mined
    receipt = client.eth.wait_for_transaction_receipt(tx_hash)

    if receipt.status == 1:
        # Check target account balance after funding
        target_balance_after = client.eth.get_balance(address)
        logger.info(f"Successfully funded {address} with {amount_in_eth} ETH")
        logger.info(
            f"Target account balance after funding: {Web3.from_wei(target_balance_after, 'ether')} ETH"
        )
        return True
    else:
        logger.error(f"Failed to fund {address}")
        return False


def fund_wallets(client, transactions, amount_in_eth=100):
    """
    Fund all unique sender wallets from a list of transactions.

    Args:
        client: Web3 client instance
        transactions: List of transaction details
        amount_in_eth: Amount of ETH to send to each wallet (default: 100)

    Returns:
        bool: True if all wallets were funded successfully, False otherwise
    """
    # Create a set of unique addresses to fund
    addresses_to_fund = set()
    for tx in transactions:
        addresses_to_fund.add(tx.get("from_address"))

    logger.info(
        f"Funding {len(addresses_to_fund)} unique addresses with {amount_in_eth} ETH each"
    )

    # Fund each unique address
    success = True
    for address in addresses_to_fund:
        if not fund_wallet(client, address, amount_in_eth):
            success = False

    return success


def should_filter_transaction(tx_id):
    """
    Check if a transaction should be filtered out based on its ID.

    Args:
        tx_id: The transaction ID to check

    Returns:
        bool: True if the transaction should be filtered out, False otherwise
    """
    filtered_transaction_ids = [
        "33a57878-eabb-4acf-920e-bc00dc193cef",
        "c93fdcb7-7068-4b2a-b5ae-4221ccb4757c",
        "55372d0e-9d91-4726-828c-f64d36fc50c4",
        # Add more transaction IDs to filter as needed
    ]

    return tx_id in filtered_transaction_ids


def get_fordefi_transactions():
    """
    Fetch transactions from ForDefi API that are in waiting_for_signing_trigger state.

    Returns:
        list: List of transactions or empty list if none found
    """
    logger.info("Fetching ForDefi transactions in waiting_for_signing_trigger state")

    # Get transactions with waiting_for_signing_trigger state
    transactions_response = get_fordefi_transactions_response(
        size=100, states="waiting_for_signing_trigger"
    )
    if not transactions_response:
        error_msg = "Failed to retrieve ForDefi transactions"
        logger.error(error_msg)
        TelegramNotifier().notify(error_msg)
        return []

    # Log pagination information
    total = transactions_response.get("total", 0)
    page = transactions_response.get("page", 0)
    size = transactions_response.get("size", 0)
    logger.info(f"Pagination info - Total: {total}, Page: {page}, Size: {size}")

    # Extract the transactions array from the response
    transactions = transactions_response.get("transactions", [])

    # Log the number of transactions
    logger.info(f"Number of transactions in response: {len(transactions)}")

    return transactions


def get_transactions_by_chains(transactions):
    """
    Process transactions and organize them by blockchain chain.

    Args:
        transactions: List of transactions to process

    Returns:
        dict: Dictionary of transactions organized by chain
    """
    transactions_by_chains = {}

    # Loop through transactions and log id and state
    for tx in transactions:
        tx_id = tx.get("id")
        if should_filter_transaction(tx_id):
            logger.info(f"Skipping transaction {tx_id}")
            continue

        tx_state = tx.get("state")
        logger.info(f"Transaction ID: {tx_id}, State: {tx_state}")

        # Get transaction details
        tx_details = get_transaction_details(tx_id)
        if not tx_details:
            error_msg = f"Failed to get details for transaction {tx_id}"
            logger.error(error_msg)
            TelegramNotifier().notify(error_msg)
            continue

        # Extract basic transaction data based on kinetic.json structure
        to_address = tx_details.get("to", {}).get("address")

        # Prioritize hex_data which is already in the correct format
        data = (
            tx_details.get("hex_data", "0x")
            if tx_details.get("hex_data")
            else tx_details.get("data", "0x")
        )
        if not data.startswith("0x"):
            data = "0x"  # Default to empty data if not a valid hex string

        value = tx_details.get("value", "0")
        from_address = tx_details.get("from", {}).get("address")

        # Store transaction in the appropriate chain bucket
        to = tx_details.get("to", {})
        contract = to.get("contract", {})
        to_contract_name = contract.get("name", "Unknown Contract")
        logger.info(f"to_contract_name: {to_contract_name}")

        token = contract.get("token", {})
        contract_token_name = token.get("name", "Unknown Token")
        logger.info(f"contract_token_name: {contract_token_name}")

        # Get the method name from parsed_data
        parsed_data = tx_details.get("parsed_data", {})
        to_parsed_data_method = parsed_data.get("method", "Unknown Method")
        logger.info(f"to_parsed_data_method: {to_parsed_data_method}")

        # Get the vault name from the "from" object
        from_obj = tx_details.get("from", {})
        vault = from_obj.get("vault", {})
        from_vault_name = vault.get("name", "Unknown Vault")
        logger.info(f"from_vault_name: {from_vault_name}")

        # Get chain information
        chain_info = None
        if "chain" in tx_details:
            # Direct chain information in the transaction
            chain_info = tx_details.get("chain", {})
        elif (
            "to" in tx_details
            and "contract" in tx_details["to"]
            and "token" in tx_details["to"]["contract"]
        ):
            # Chain information from token contract
            chain_info = (
                tx_details["to"]["contract"]["token"]
                .get("address", {})
                .get("chain", {})
            )
        elif (
            "expected_result" in tx_details
            and "gas_debit" in tx_details["expected_result"]
        ):
            # Chain information from gas debit
            chain_info = (
                tx_details["expected_result"]["gas_debit"]
                .get("priced_asset", {})
                .get("asset_identifier", {})
                .get("details", {})
                .get("chain", {})
            )

        if not chain_info:
            error_msg = f"Could not find chain information for transaction {tx_id}"
            logger.error(error_msg)
            TelegramNotifier().notify(error_msg, json.dumps(tx_details, indent=2))
            logger.debug(f"Transaction details: {json.dumps(tx_details, indent=2)}")
            continue

        chain_name = chain_info.get("name", "Unknown").upper()
        chain_id = chain_info.get("chain_id")
        logger.info(f"chain_name: {chain_name}")

        if chain_name not in transactions_by_chains:
            transactions_by_chains[chain_name] = {}
            transactions_by_chains[chain_name]["chain_id"] = chain_id
            transactions_by_chains[chain_name]["transaction_details"] = []

        # Store the detailed transaction data with all the extracted information
        transactions_by_chains[chain_name]["transaction_details"].append(
            {
                "id": tx_id,
                "to_address": to_address,
                "data": data,
                "value": value,
                "from_address": from_address,
                "to_contract_name": to_contract_name,
                "from_vault_name": from_vault_name,
                "contract_token_name": contract_token_name,
                "to_parsed_data_method": to_parsed_data_method,
            }
        )

    return transactions_by_chains


def setup_simulation_environment(
    chain_name, provider_url, chain_id, should_fund_wallets, transactions
):
    """
    Set up the simulation environment with Anvil and Web3 client.

    Args:
        chain_name: Name of the blockchain chain
        provider_url: URL of the blockchain provider
        chain_id: ID of the blockchain chain
        should_fund_wallets: Whether to fund sender wallets
        transactions: List of transactions to simulate

    Returns:
        tuple: (anvil_process, web3_client) or (None, None) if setup fails
    """
    try:
        anvil_process = start_anvil(provider_url, chain_id)
        client = get_web3_client_with_retry()

        # Fund wallets if needed
        if should_fund_wallets:
            logger.info("Funding wallets for simulation...")
            fund_wallets(client, transactions)

        return anvil_process, client
    except Exception as e:
        error_msg = f"Failed to start Anvil for chain {chain_name}"
        logger.error(f"{error_msg}: {str(e)}")
        TelegramNotifier().notify(error_msg, str(e))
        return None, None


def process_transaction(client, tx):
    """
    Process a single transaction for simulation.

    Args:
        client: Web3 client instance
        tx: Transaction details

    Returns:
        tuple: (simulation_success, error_details, tx_data) where tx_data contains extracted transaction data
    """
    from_address = tx.get("from_address")
    to_address = tx.get("to_address")
    value = (
        int(tx.get("value", "0"), 16)
        if tx.get("value", "0").startswith("0x")
        else int(tx.get("value", "0"))
    )
    data = tx.get("data", "0x")
    tx_id = tx.get("id")
    tx_method = tx.get("to_parsed_data_method")
    tx_contract = tx.get("to_contract_name")
    tx_vault = tx.get("from_vault_name")
    tx_token = tx.get("contract_token_name")

    logger.info(
        f"Simulating transaction {tx_id} from vault '{tx_vault}' to contract '{tx_contract}' method '{tx_method}'"
    )
    nonce = client.eth.get_transaction_count(from_address)

    # Check balance before simulation
    balance = client.eth.get_balance(from_address)
    logger.info(
        f"Wallet balance before simulation: {Web3.from_wei(balance, 'ether')} ETH"
    )

    # Use a higher gas limit for complex transactions
    result = simulate_transaction_inner(
        client,
        {
            "from": from_address,
            "to": to_address,
            "value": value,
            "data": data,
            "nonce": nonce,
        },
    )

    # Handle different return types
    if isinstance(result, tuple) and len(result) == 2:
        simulation_success, error_details = result
    else:
        # If it's just a boolean
        simulation_success = result
        error_details = None if simulation_success else "Unknown error"

    tx_data = {
        "id": tx_id,
        "from_address": from_address,
        "to_address": to_address,
        "vault": tx_vault,
        "contract": tx_contract,
        "method": tx_method,
        "token": tx_token,
    }

    return simulation_success, error_details, tx_data


def record_simulation_result(simulation_success, error_details, tx_data):
    """
    Record and log the simulation result.

    Args:
        simulation_success: Whether the simulation was successful
        error_details: Error details if simulation failed
        tx_data: Transaction data

    Returns:
        dict: Simulation result
    """
    result = {
        "id": tx_data["id"],
        "success": simulation_success,
        "vault": tx_data["vault"],
        "contract": tx_data["contract"],
        "method": tx_data["method"],
        "token": tx_data["token"],
        "error": error_details if not simulation_success else None,
    }

    if not simulation_success:
        error_msg = f"Transaction simulation failed for tx {tx_data['id']} from vault '{tx_data['vault']}' ({tx_data['from_address']}) to contract '{tx_data['contract']}' ({tx_data['to_address']}) method '{tx_data['method']}', token: '{tx_data['token']}'"
        if error_details:
            error_msg += f"\nError: {error_details}"
        logger.error(error_msg)
        TelegramNotifier().notify(error_msg)
    else:
        logger.info(f"Transaction simulation successful for tx {tx_data['id']}")

    return result


def handle_simulation_error(tx, e):
    """
    Handle exceptions during simulation.

    Args:
        tx: Transaction details
        e: Exception

    Returns:
        dict: Error simulation result
    """
    tx_id = tx.get("id")
    error_msg = f"Error simulating transaction {tx_id}"
    logger.error(f"{error_msg}: {str(e)}")
    TelegramNotifier().notify(error_msg, str(e))

    return {
        "id": tx_id,
        "success": False,
        "error": str(e),
        "vault": tx.get("from_vault_name", "Unknown"),
        "contract": tx.get("to_contract_name", "Unknown"),
        "method": tx.get("to_parsed_data_method", "Unknown"),
    }


def simulate_fordefi_chain(chain_name, chain_data, should_fund_wallets=False):
    """
    Simulate ForDefi transactions for a specific blockchain chain.

    Args:
        chain_name: Name of the blockchain chain
        chain_data: Dictionary containing chain ID and transaction details
        should_fund_wallets: Whether to fund sender wallets before simulation (default: False)

    Returns:
        list: List of simulation results for the chain
    """
    simulation_results = []

    provider_url = ""
    if chain_name in PROVIDER_URL:
        provider_url = PROVIDER_URL[chain_name]
        logger.info(f"provider_url: {provider_url}")
    else:
        error_msg = f"Chain {chain_name} not found in PROVIDER_URL"
        logger.error(error_msg)
        TelegramNotifier().notify(error_msg)
        return simulation_results

    chain_id = chain_data["chain_id"]
    transactions = chain_data["transaction_details"]
    logger.info(f"provider_url: {provider_url}")
    logger.info(f"chain_id: {chain_id}")

    anvil_process, client = setup_simulation_environment(
        chain_name, provider_url, chain_id, should_fund_wallets, transactions
    )
    if not anvil_process or not client:
        return simulation_results

    for tx in transactions:
        try:
            simulation_success, error_details, tx_data = process_transaction(client, tx)
            simulation_results.append(
                record_simulation_result(simulation_success, error_details, tx_data)
            )
        except Exception as e:
            simulation_results.append(handle_simulation_error(tx, e))

    try:
        stop_process(anvil_process)
    except Exception as e:
        logger.error(f"Error stopping Anvil process: {str(e)}")

    return simulation_results


def simulate_fordefi(should_fund_wallets=False):
    """
    Handler for ForDefi transaction simulation.

    Args:
        should_fund_wallets: Whether to fund sender wallets before simulation (default: False)

    Returns:
        Dictionary with simulation results
    """
    logger.info("Starting ForDefi transaction simulation")

    # Get transactions with waiting_for_signing_trigger state
    transactions = get_fordefi_transactions()

    if not transactions:
        logger.info("No transactions found in waiting_for_signing_trigger state")
        return {
            "statusCode": 200,
            "body": json.dumps({"message": "No transactions to simulate"}),
        }

    # Organize transactions by chain
    transactions_by_chains = get_transactions_by_chains(transactions)

    if not transactions_by_chains:
        error_msg = "No valid transactions found to simulate"
        logger.error(error_msg)
        return {"statusCode": 200, "body": json.dumps({"message": error_msg})}

    logger.info(transactions_by_chains)
    all_simulation_results = []

    # Simulate transactions for each chain
    for chain_name, chain_data in transactions_by_chains.items():
        chain_simulation_results = simulate_fordefi_chain(
            chain_name, chain_data, should_fund_wallets
        )
        all_simulation_results.extend(chain_simulation_results)

    return {
        "statusCode": 200,
        "body": json.dumps(
            {
                "message": "ForDefi transaction simulation completed",
                "results": all_simulation_results,
            }
        ),
    }


def lambda_handler(event, context):
    logger.info("Calling lambda_handler in simulator")

    try:
        simulate_fordefi()
    except Exception as e:
        logger.error(f"Error simulating ForDefi transactions: {e}")
        TelegramNotifier().notify(f"Error simulating ForDefi transactions: {e}")

    agents_by_chains = {}
    for agent in LIST_OF_AGENTS:
        try:
            agent: Agent = agent()
            agent.chain_name
        except Exception as e:
            logger.error(f"Error initializing agent {agent.__name__}: {e}")
            TelegramNotifier().notify(f"Error initializing agent {agent.__name__}: {e}")
            continue

        if agent.chain_name not in agents_by_chains:
            agents_by_chains[agent.chain_name] = []
        agents_by_chains[agent.chain_name].append(agent)

    logger.info(agents_by_chains)

    for chain_name, agents in agents_by_chains.items():
        provider_url = PROVIDER_URL[chain_name]
        anvil_process = start_anvil(
            provider_url, static_params_index[agents[0].static_param_key]["chain_id"]
        )

        client = get_web3_client_with_retry()

        for agent in agents:
            simulation_result = agent.simulate_transaction(client)
            if not simulation_result:
                TelegramNotifier().notify(
                    f"Simulation failed for agent {agent.__class__.__name__}"
                )

        stop_process(anvil_process)


if __name__ == "__main__":
    lambda_handler(None, None)
