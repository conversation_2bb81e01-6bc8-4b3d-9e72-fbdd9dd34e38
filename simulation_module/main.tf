provider "aws" {
  region = "eu-central-1"
}


resource "aws_s3_bucket" "terraform_state" {
  bucket = "hypernative-simulator-terraform-state"
}

resource "aws_s3_bucket_versioning" "versioning" {
  bucket = aws_s3_bucket.terraform_state.bucket

  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "encryption" {
  bucket = aws_s3_bucket.terraform_state.bucket

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

terraform {
  backend "s3" {
    bucket  = "hypernative-simulator-terraform-state"
    key     = "terraform.tfstate"
    region  = "eu-central-1"
    encrypt = true
  }
}

# log group for the lambda function
resource "aws_cloudwatch_log_group" "hypernative_simulator_log_group" {
  name              = "/aws/lambda/hypernative_simulator"
  retention_in_days = 30
}
 
resource "aws_lambda_function" "hypernative_simulator" {
  function_name = "hypernative_simulator"
  package_type  = "Image"
  role          = "arn:aws:iam::236840575805:role/lambda_exec_role"
  image_uri     = "236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_simulator:latest"
  architectures = ["x86_64"]
  timeout       = 300
  memory_size   = 1024
  tags = {
    Name = "hypernative_simulator"
  }

  vpc_config {
    subnet_ids         = ["subnet-0c946ac67c9c83a48"]
    security_group_ids = ["sg-0ffc293f028774d92"]
  }

  environment {
    variables = {
      RDS_SECRET_NAME="dev/jobs/rds"
    }
  }
}

# schedule the lambda function to run every 30 minutes
resource "aws_cloudwatch_event_rule" "hypernative_simulator_schedule" {
  name                = "hypernative_simulator_schedule"
  schedule_expression = "cron(0/30 * * * ? *)"
}

resource "aws_cloudwatch_event_target" "hypernative_simulator_target" {
  rule      = aws_cloudwatch_event_rule.hypernative_simulator_schedule.name
  target_id = "hypernative_simulator_target"
  arn       = aws_lambda_function.hypernative_simulator.arn
}

# permissions to trigger the lambda function
resource "aws_lambda_permission" "hypernative_simulator_schedule" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.hypernative_simulator.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.hypernative_simulator_schedule.arn
}

resource "aws_cloudwatch_log_metric_filter" "hypernative_simulator_error_log_filter" {
  name           = "hypernative_simulator_error_log_filter"
  pattern        = "\"error\""
  log_group_name = "/aws/lambda/hypernative_simulator"
  metric_transformation {
    name      = "ErrorOccurrences"
    namespace = "hypernative-simulator"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "hypernative_simulator_error_alarm" {
  alarm_name                = "hypernative-simulator-error-alarm"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  evaluation_periods        = "1"
  metric_name               = "ErrorOccurrences"
  namespace                 = "hypernative-simulator"
  period                    = 300
  statistic                 = "Sum"
  threshold                 = "1"
  alarm_description         = "This metric monitors log errors"
  actions_enabled           = true
  alarm_actions             = [aws_sns_topic.alerts.arn]
}

resource "aws_sns_topic" "alerts" {
  name = "hypernative_simulator_alerts"
}

resource "aws_sns_topic_subscription" "gasan_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "colin_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "jashiel_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_policy" "default" {
  arn    = aws_sns_topic.alerts.arn
  policy = data.aws_iam_policy_document.sns_policy.json
}

data "aws_iam_policy_document" "sns_policy" {
  statement {
    actions = ["SNS:Publish"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudwatch.amazonaws.com"]
    }
    resources = [aws_sns_topic.alerts.arn]
  }
}
