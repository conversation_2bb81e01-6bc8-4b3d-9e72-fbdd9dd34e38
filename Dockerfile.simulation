# Use AWS Lambda Python 3.12 Base Image
FROM public.ecr.aws/lambda/python:3.12

# Install needed dependencies
RUN dnf install -y git tar gcc-c++ make

# Install setuptools to resolve pkg_resources error
RUN pip install --upgrade pip setuptools

# Install Foundry (includes Anvil)
RUN curl -L https://foundry.paradigm.xyz | bash && \
    /root/.foundry/bin/foundryup

# Move Anvil to a globally accessible directory
RUN mkdir -p /usr/local/bin && \
    cp /root/.foundry/bin/anvil /usr/local/bin/anvil && \
    chmod +x /usr/local/bin/anvil

# Verify installation
RUN ls -l /usr/local/bin/anvil
RUN anvil --version

# Set PATH for AWS Lambda
ENV PATH="/usr/local/bin:${PATH}"

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set CMD
CMD ["simulation_module/simulator.lambda_handler"]
