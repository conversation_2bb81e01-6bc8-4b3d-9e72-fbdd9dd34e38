import requests
import os

from helpers.log import get_logger

logger = get_logger(__name__)


def trigger_transaction_signing(alert_data):
    # Extract relevant data from the alert
    transaction_id = alert_data["headers"]["fordefi-transaction-id"]

    # Get the bearer token from the environment
    bearer_token = os.getenv("FORDEFI_BEARER_TOKEN")

    # Call ForDefi API to create a transaction
    fordefi_url = (
        f"https://api.fordefi.com/api/v1/transactions/{transaction_id}/trigger-signing"
    )
    headers = {
        "Authorization": f"Bearer {bearer_token}",
    }

    # Make a POST request to the ForDefi API
    response = requests.post(fordefi_url, headers=headers)

    # Check if the transaction was created successfully
    if response.status_code == 204:
        logger.info("Transaction created successfully.")
    else:
        logger.error(f"Failed to create transaction: {response.content}")
