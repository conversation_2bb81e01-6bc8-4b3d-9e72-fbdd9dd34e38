import os
from dotenv import load_dotenv

load_dotenv()

from helpers.load_cloud_env import load_cloud_env

load_cloud_env()

PROVIDER_URL = {
    "ETHEREUM": os.getenv("ETH_PROVIDER_URL"),
    "ARBITRUM": os.getenv("ARB_PROVIDER_URL"),
    "MANTLE": os.getenv("MANTLE_PROVIDER_URL"),
    "MODE": os.getenv("MODE_PROVIDER_URL"),
    "BASE": os.getenv("BASE_PROVIDER_URL"),
    "AVALANCHE": os.getenv("AVALANCHE_PROVIDER_URL"),
    "FLARE": os.getenv("FLARE_PROVIDER_URL"),
    "SONIC": os.getenv("SONIC_PROVIDER_URL"),
}
