from typing import Optional
import requests
import os
import sys

sys.path.append(os.getcwd())

from helpers.log import get_logger

from helpers.load_cloud_env import load_cloud_env

load_cloud_env()

logger = get_logger(__name__)


def get_fordefi_transactions_response(
    size=100,
    page=1,
    states=None,
    from_date=None,
    to_date=None,
) -> Optional[dict]:
    """
    Retrieve transactions from ForDefi API with optional filtering.

    Args:
        page_size: Number of transactions per page
        page: Page number to retrieve
        states: Filter by transaction state (e.g., "waiting_for_signing_trigger")
        from_date: Start date for filtering (ISO format string)
        to_date: End date for filtering (ISO format string)

    Returns:
        Dictionary containing transaction data or None if request fails
    """
    try:
        bearer_token = os.getenv("FORDEFI_BEARER_TOKEN")
        url = "https://api.fordefi.com/api/v1/transactions"

        if not bearer_token:
            raise ValueError("Missing FORDEFI_BEARER_TOKEN")

        headers = {
            "Authorization": f"Bearer {bearer_token}",
        }

        params = {
            "size": size,
            "page": page,
        }

        if states:
            params["states"] = states

        if from_date:
            params["from_date"] = from_date

        if to_date:
            params["to_date"] = to_date

        response = requests.get(url, headers=headers, params=params)

        if response.status_code == 200:
            return response.json()
        else:
            logger.error(
                f"Failed to retrieve transactions: {response.status_code} - {response.text}"
            )
            return None

    except Exception as e:
        logger.error(f"Error retrieving transactions: {str(e)}", exc_info=True)
        return None


def get_transaction_details(transaction_id) -> Optional[dict]:
    """
    Retrieve details for a specific transaction from ForDefi API.

    Args:
        transaction_id: ID of the transaction to retrieve

    Returns:
        Dictionary containing transaction details or None if request fails
    """
    try:
        bearer_token = os.getenv("FORDEFI_BEARER_TOKEN")
        url = f"https://api.fordefi.com/api/v1/transactions/{transaction_id}"

        if not bearer_token:
            raise ValueError("Missing FORDEFI_BEARER_TOKEN")

        headers = {
            "Authorization": f"Bearer {bearer_token}",
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            return response.json()
        else:
            logger.error(
                f"Failed to retrieve transaction details: {response.status_code} - {response.text}"
            )
            return None

    except Exception as e:
        logger.error(f"Error retrieving transaction details: {str(e)}", exc_info=True)
        return None
