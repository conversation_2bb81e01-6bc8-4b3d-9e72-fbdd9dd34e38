from web3 import Web3
from web3.exceptions import ContractLogicError, ContractCustomError, Web3ValidationError
import requests

from helpers.log import get_logger

logger = get_logger(__name__)


def supports_eip1559(client: Web3) -> bool:
    try:
        block = client.eth.get_block("pending")
        return "baseFeePerGas" in block
    except Exception:
        return False


def simulate_transaction(
    client: Web3,
    contract_function=None,
    params=None,
    from_address=None,
    gas_multiplier=5,
):
    """
    Simulates a transaction using a contract function and parameters.

    Args:
        client: Web3 client instance
        contract_function: Contract function to call
        params: Parameters for the contract function (list or tuple)
        from_address: Address to send the transaction from
        gas_multiplier: Multiplier for gas estimation (default: 5)

    Returns:
        bool: True if simulation was successful, False otherwise
    """
    # Handle the case where the first argument is already a transaction dict
    logger.info("--------if isinstance--------")
    if isinstance(contract_function, dict) and params is None:
        return simulate_transaction_inner(client, contract_function, gas_multiplier)

    try:
        # Build the function call based on params type
        if isinstance(params, list):
            function_call = contract_function(*params)
        elif isinstance(params, tuple):
            function_call = contract_function(params)
        else:
            raise ValueError("params must be a list or a tuple")

        # Create transaction dict with basic info
        tx = {
            "from": from_address,
            "nonce": client.eth.get_transaction_count(from_address),
        }

        # Build the transaction
        built_transaction = function_call.build_transaction(tx)

        # Use simulate_transaction_inner to handle the rest
        result = simulate_transaction_inner(client, built_transaction, gas_multiplier)

        # If simulation failed, try to get revert reason
        if not result:
            try:
                logger.info("Trying to retrieve the revert reason...")
                if isinstance(params, list):
                    contract_function(*params).call({"from": from_address})
                elif isinstance(params, tuple):
                    contract_function(params).call({"from": from_address})
                logger.info("No revert reason found")
            except ContractLogicError as e:
                logger.error(f"Revert reason: {e}")
            except ContractCustomError as e:
                logger.error(f"Revert reason: {e}")

        return result

    except ValueError as e:
        logger.error(f"Invalid parameters: {e}", exc_info=True)
        return False
    except Web3ValidationError as e:
        logger.error(f"Failed to build transaction: {e}", exc_info=True)
        return False
    except ContractLogicError as e:
        logger.error(f"Contract logic error: {e}", exc_info=True)
        return False
    except ContractCustomError as e:
        logger.error(f"Contract custom error: {e}", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"Failed to build transaction: {e}", exc_info=True)
        return False


def simulate_transaction_inner(client: Web3, tx, gas_multiplier=5):
    """
    Inner function to simulate a transaction.

    Args:
        client: Web3 client instance
        tx: Transaction dictionary
        gas_multiplier: Multiplier for gas estimation (default: 5)

    Returns:
        tuple: (bool, str) - Success status and error message if any
    """
    error_message = None
    try:
        # Log the transaction details for debugging
        logger.info(f"Transaction details for gas estimation: {tx}")

        # Check sender balance before doing anything else
        sender_balance = client.eth.get_balance(tx["from"])
        logger.info(
            f"Sender balance: {Web3.from_wei(sender_balance, 'ether')} ETH ({sender_balance} wei)"
        )

        # Set gas price based on EIP-1559 or legacy
        if supports_eip1559(client):
            latest_block = client.eth.get_block("pending")
            base_fee_per_gas = latest_block["baseFeePerGas"]
            priority_fee = Web3.to_wei(0, "gwei")
            base_fee_with_buffer = int(base_fee_per_gas * 1.2)
            max_fee_per_gas = base_fee_with_buffer + priority_fee
            tx["maxFeePerGas"] = max_fee_per_gas
            tx["maxPriorityFeePerGas"] = priority_fee
            logger.info(
                f"Using EIP-1559 gas: base_fee={base_fee_per_gas} wei, max_fee={max_fee_per_gas} wei, priority_fee={priority_fee} wei"
            )
            logger.info(
                f"Using EIP-1559 gas: base_fee={Web3.from_wei(base_fee_per_gas, 'gwei')} gwei, max_fee={Web3.from_wei(max_fee_per_gas, 'gwei')} gwei"
            )

            # Calculate maximum affordable gas based on balance
            value = tx.get("value", 0)
            max_affordable_gas = (sender_balance - value) // max_fee_per_gas
            logger.info(
                f"Maximum affordable gas: {max_affordable_gas} (calculation: ({sender_balance} - {value}) / {max_fee_per_gas})"
            )
        else:
            gas_price = client.eth.gas_price
            tx["gasPrice"] = gas_price
            logger.info(
                f"Using legacy gas price: {gas_price} wei ({Web3.from_wei(gas_price, 'gwei')} gwei)"
            )

            # Calculate maximum affordable gas based on balance
            value = tx.get("value", 0)
            max_affordable_gas = (sender_balance - value) // gas_price
            logger.info(
                f"Maximum affordable gas: {max_affordable_gas} (calculation: ({sender_balance} - {value}) / {gas_price})"
            )

        # If gas is already set in the transaction, check if it's affordable
        if "gas" in tx:
            logger.info(f"Gas already specified in transaction: {tx['gas']}")
            if tx["gas"] > max_affordable_gas:
                logger.warning(
                    f"Specified gas ({tx['gas']}) exceeds affordable gas ({max_affordable_gas}). Adjusting."
                )
                tx["gas"] = int(
                    max_affordable_gas * 0.9
                )  # Use 90% of affordable gas to be safe
            logger.info(f"Using specified gas limit: {tx['gas']}")
        else:
            try:
                # Try to get revert reason using eth_call before estimation
                try:
                    call_tx = tx.copy()
                    # Remove gas-related fields for eth_call
                    for field in [
                        "gas",
                        "maxFeePerGas",
                        "maxPriorityFeePerGas",
                        "gasPrice",
                    ]:
                        if field in call_tx:
                            del call_tx[field]

                    logger.info("Attempting eth_call to check for reverts...")
                    client.eth.call(call_tx)
                    logger.info("eth_call succeeded, transaction should be valid")
                except Exception as call_error:
                    logger.warning(
                        f"eth_call failed: {call_error}. This may indicate why gas estimation will fail."
                    )

                # Now try the actual gas estimation
                logger.info("Attempting gas estimation...")
                estimated_gas = client.eth.estimate_gas(tx)
                gas_with_multiplier = int(estimated_gas * gas_multiplier)

                # Ensure we don't exceed affordable gas
                if gas_with_multiplier > max_affordable_gas:
                    logger.warning(
                        f"Gas with multiplier ({gas_with_multiplier}) exceeds affordable gas ({max_affordable_gas}). Using affordable gas."
                    )
                    tx["gas"] = int(
                        max_affordable_gas * 0.9
                    )  # Use 90% of affordable gas to be safe
                else:
                    tx["gas"] = gas_with_multiplier

                logger.info(
                    f"Gas estimation successful: {estimated_gas}, using {tx['gas']} with multiplier"
                )
            except Exception as e:
                error_hex = None
                error_message = str(e)

                # Try to extract the error code if it's in hex format
                if "0x" in error_message:
                    import re

                    hex_match = re.search(r"(0x[0-9a-fA-F]+)", error_message)
                    if hex_match:
                        error_hex = hex_match.group(1)

                logger.warning(
                    f"Gas estimation failed: {e}. Error code: {error_hex}. Setting appropriate gas limit."
                )

                # Try to decode the error using etherface.io
                if error_hex:
                    logger.info(f"Attempting to decode error: {error_hex}")
                    decoded = lookup_selector_etherface(error_hex)
                    if decoded:
                        logger.warning(f"Known error: {decoded}")

                # Set a reasonable gas limit based on available balance
                # For complex transactions, we need a high gas limit, but we must ensure it's affordable
                desired_gas = 2_000_000  # Desired gas limit for complex transactions

                if desired_gas > max_affordable_gas:
                    logger.warning(
                        f"Desired gas ({desired_gas}) exceeds affordable gas ({max_affordable_gas}). Using affordable gas."
                    )
                    tx["gas"] = int(
                        max_affordable_gas * 0.9
                    )  # Use 90% of affordable gas to be safe
                else:
                    tx["gas"] = desired_gas

                logger.info(
                    f"Using fallback gas limit: {tx['gas']}. Final tx: {tx}"
                )
    except Web3ValidationError as e:
        error_message = f"Failed to build transaction: {e}"
        logger.error(error_message, exc_info=True)
        return False, error_message
    except ContractLogicError as e:
        error_message = f"Failed to send transaction: {e}"
        logger.error(error_message, exc_info=True)
        return False, error_message
    except ContractCustomError as e:
        error_message = f"Failed to send transaction: {e}"
        logger.error(error_message, exc_info=True)
        return False, error_message
    except Exception as e:
        error_message = f"Failed to build transaction: {e}"
        logger.error(error_message, exc_info=True)
        return False, error_message

    try:
        # For simulation purposes, let's use a lower gas price but ensure it's above the base fee
        if supports_eip1559(client):
            # Get the latest block to check current base fee
            latest_block = client.eth.get_block("pending")
            base_fee_per_gas = latest_block["baseFeePerGas"]

            # Ensure our max fee is at least the base fee plus a small buffer
            min_max_fee = int(base_fee_per_gas * 1.1)  # 10% buffer
            simulation_max_fee = max(Web3.to_wei(0.1, "gwei"), min_max_fee)

            # Set priority fee to a small value but ensure total is above base fee
            simulation_priority_fee = Web3.to_wei(0.01, "gwei")

            tx["maxFeePerGas"] = simulation_max_fee
            tx["maxPriorityFeePerGas"] = simulation_priority_fee

            logger.info(
                f"Adjusted gas prices for simulation: maxFeePerGas={Web3.from_wei(tx['maxFeePerGas'], 'gwei')} gwei, maxPriorityFeePerGas={Web3.from_wei(tx['maxPriorityFeePerGas'], 'gwei')} gwei"
            )
            logger.info(
                f"Current base fee: {Web3.from_wei(base_fee_per_gas, 'gwei')} gwei"
            )
        else:
            # For legacy transactions, ensure gas price is at least the network's gas price
            min_gas_price = client.eth.gas_price
            simulation_gas_price = max(Web3.to_wei(0.1, "gwei"), min_gas_price)
            tx["gasPrice"] = simulation_gas_price
            logger.info(
                f"Adjusted gas price for simulation: {Web3.from_wei(tx['gasPrice'], 'gwei')} gwei"
            )

        # Recalculate max affordable gas with new prices
        value = tx.get("value", 0)
        if supports_eip1559(client):
            new_max_affordable_gas = (sender_balance - value) // tx["maxFeePerGas"]
        else:
            new_max_affordable_gas = (sender_balance - value) // tx["gasPrice"]

        logger.info(
            f"New maximum affordable gas with adjusted prices: {new_max_affordable_gas}"
        )

        # Adjust gas limit if needed
        if tx["gas"] > new_max_affordable_gas:
            tx["gas"] = int(new_max_affordable_gas * 0.9)
            logger.info(f"Adjusted gas limit to: {tx['gas']}")

        tx_hash = client.eth.send_transaction(tx)
        logger.info(f"Simulated transaction hash: 0x{tx_hash.hex()}")
    except ContractLogicError as e:
        error_message = f"Failed to send transaction: {e}"
        logger.error(error_message, exc_info=True)
        return False, error_message
    except ContractCustomError as e:
        error_message = f"Failed to send transaction: {e}"
        logger.error(error_message, exc_info=True)
        return False, error_message
    except Exception as e:
        error_message = f"Failed to send transaction: {e}"
        logger.error(error_message, exc_info=True)
        return False, error_message

    receipt = client.eth.wait_for_transaction_receipt(tx_hash)

    if receipt.status != 1:
        logger.error(
            f"Simulated transaction failed. Receipt status: {receipt.status}"
        )
        logger.info(f"Gas used: {receipt.gasUsed}")

        revert_reason = "Unknown error"

        # Try to get more detailed error information
        try:
            # First try to use debug_traceTransaction if available
            try:
                trace = client.provider.make_request(
                    "debug_traceTransaction", [tx_hash.hex()]
                )
                if "result" in trace and "structLogs" in trace["result"]:
                    for step in trace["result"]["structLogs"]:
                        if "error" in step and step["error"] != "":
                            revert_reason = f"Trace error: {step['error']}"
                            logger.error(revert_reason)
            except Exception as trace_error:
                logger.warning(f"Could not get transaction trace: {trace_error}")

            # Try to decode the error using eth_call
            try:
                # Create a copy of the transaction for eth_call
                call_tx = tx.copy()
                # Remove gas-related fields for eth_call
                for field in [
                    "gas",
                    "maxFeePerGas",
                    "maxPriorityFeePerGas",
                    "gasPrice",
                ]:
                    if field in call_tx:
                        del call_tx[field]

                logger.info("Trying to decode revert reason using eth_call...")
                client.eth.call(call_tx)
            except Exception as call_error:
                error_str = str(call_error)
                logger.error(f"Revert reason from eth_call: {error_str}")
                revert_reason = f"Revert reason: {error_str}"

                # Try to extract revert reason from the error string
                import re

                # Look for standard revert reason format
                revert_match = re.search(r"revert: (.+?)(?:\n|$)", error_str)
                if revert_match:
                    revert_reason = f"Decoded revert reason: {revert_match.group(1)}"
                    logger.error(revert_reason)

                # Look for custom error selectors
                selector_match = re.search(r"Reverted 0x([0-9a-fA-F]{8})", error_str)
                if selector_match:
                    error_selector = f"0x{selector_match.group(1)}"
                    logger.error(f"Custom error selector: {error_selector}")
                    # Always start with 4byte.directory lookup
                    decoded = lookup_selector_4byte(error_selector)
                    if decoded:
                        revert_reason = f"Decoded from 4byte.directory: {decoded}"
                        logger.error(revert_reason)
                    else:
                        # Fallback to etherface.io
                        decoded2 = lookup_selector_etherface(error_selector)
                        if decoded2:
                            revert_reason = f"Decoded from etherface.io: {decoded2}"
                            logger.error(revert_reason)
                        else:
                            logger.error(f"Unknown selector: {error_selector}")
        except Exception as e:
            logger.error(f"Error while trying to decode revert reason: {e}")

        error_message = revert_reason
        return False, error_message
    else:
        logger.info("Successfully simulated transaction")
        return True


def lookup_selector_4byte(selector):
    """
    Lookup a 4-byte selector on 4byte.directory. Returns the first text signature if found, else None.
    """
    if not (
        isinstance(selector, str) and selector.startswith("0x") and len(selector) == 10
    ):
        return None
    url = f"https://www.4byte.directory/api/v1/signatures/?hex_signature={selector}"
    try:
        resp = requests.get(url, timeout=5)
        resp.raise_for_status()
        results = resp.json().get("results", [])
        if results:
            return results[0]["text_signature"]
    except Exception as e:
        logger.warning(f"4byte.directory lookup failed: {e}")
    return None


def lookup_selector_etherface(selector):
    """
    Lookup a 4-byte selector on etherface.io. Returns the first signature if found, else None.
    """
    if not (
        isinstance(selector, str) and selector.startswith("0x") and len(selector) == 10
    ):
        return None
    url = f"https://api.etherface.io/v1/signatures/{selector}"
    try:
        resp = requests.get(url, timeout=5)
        resp.raise_for_status()
        data = resp.json()
        if data and "signatures" in data and data["signatures"]:
            return data["signatures"][0]["name"]
    except Exception as e:
        logger.warning(f"etherface.io lookup failed: {e}")
    return None
