import os
import sys

sys.path.append(os.getcwd())
from web3 import Web3
from web3.exceptions import ContractLogicError, ContractCustomError, Web3ValidationError

from dotenv import load_dotenv

load_dotenv()

from helpers.load_cloud_env import load_cloud_env

load_cloud_env()

from helpers.log import get_logger

logger = get_logger(__name__)


def execute_transaction(
    client: Web3, contract_function, params, from_address, gas_multiplier=5
):
    key = os.getenv("PRIVATE_KEY")
    try:
        built_transaction = {}
        built_transaction["data"] = "error while building transaction"

        # Fetch the latest block
        latest_block = client.eth.get_block("latest")
        base_fee_per_gas = latest_block["baseFeePerGas"]
        priority_fee = Web3.to_wei(1, "gwei")  # Set a reasonable priority fee
        max_fee_per_gas = base_fee_per_gas + priority_fee

        tx = {
            "from": from_address,
            "nonce": client.eth.get_transaction_count(from_address),
            "maxFeePerGas": max_fee_per_gas,
            "maxPriorityFeePerGas": priority_fee,
        }

        # estimated_gas = client.eth.estimate_gas(tx)
        # tx["gas"] = int(estimated_gas * gas_multiplier)

        if isinstance(params, list):
            function_call = contract_function(*params)
        elif isinstance(params, tuple):
            function_call = contract_function(params)
        else:
            raise ValueError("params must be a list or a tuple")

        built_transaction = function_call.build_transaction(tx)

        logger.info(f"Built transaction: {tx}")
    except Web3ValidationError as e:
        logger.error(f"Failed to build transaction: {e}", exc_info=True)
        return built_transaction["data"]
    except ContractLogicError as e:
        logger.error(f"Failed to send transaction: {e}", exc_info=True)
        return built_transaction["data"]
    except ContractCustomError as e:
        logger.error(f"Failed to send transaction: {e}", exc_info=True)
        return built_transaction["data"]
    except Exception as e:
        logger.error(f"Failed to build transaction: {e}", exc_info=True)
        return built_transaction["data"]

    try:
        signed_tx = client.eth.account.sign_transaction(built_transaction, key)
        # NOTE: FOR PROD
        client.eth.send_raw_transaction(signed_tx.raw_transaction)
        tx_hash = client.to_hex(client.keccak(signed_tx.raw_transaction))

        # NOTE: FOR VSCODE DEBUGGER ONLY
        # client.eth.send_raw_transaction(signed_tx.rawTransaction)
        # tx_hash = client.to_hex(client.keccak(signed_tx.rawTransaction))
        logger.info(f"Executed transaction hash: {tx_hash}")
    except ContractLogicError as e:
        logger.error(f"Failed to send transaction: {e}", exc_info=True)
        return tx_hash
    except ContractCustomError as e:
        logger.error(f"Failed to send transaction: {e}", exc_info=True)
        return tx_hash
    except Exception as e:
        logger.error(f"Failed to send transaction: {e}", exc_info=True)
        return tx_hash

    receipt = client.eth.wait_for_transaction_receipt(tx_hash)

    if receipt.status != 1:
        logger.error(f"Executed transaction failed. Receipt status: {receipt.status}")
        logger.info(f"Gas used: {receipt.gasUsed}")
        trace = client.provider.make_request(
            "debug_traceTransaction", [tx_hash]
        )  # NOTE: For VSCode debugger only
        # trace = client.provider.make_request('debug_traceTransaction', ["0x" + tx_hash.hex()])
        for step in trace["result"]["structLogs"]:
            if "error" not in step:
                continue
            error = step["error"]
            if error != "":
                logger.error(f"Error: {error}")

        try:
            logger.info("Trying to retrieve the revert reason...")
            if isinstance(params, list):
                contract_function(*params).call(
                    {
                        "from": from_address,
                    }
                )
            elif isinstance(params, tuple):
                contract_function(params).call(
                    {
                        "from": from_address,
                    }
                )
            else:
                raise ValueError("params must be a list or a tuple")
            logger.info("No revert reason found")
        except ContractLogicError as e:
            logger.error(f"Revert reason: {e}")
        except ContractCustomError as e:
            logger.error(f"Revert reason: {e}")

        return tx_hash
    else:
        logger.info(f"Successfully executed transaction: {receipt}")
        return tx_hash
