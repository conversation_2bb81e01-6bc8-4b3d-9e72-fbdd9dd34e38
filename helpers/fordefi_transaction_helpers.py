# transaction_helpers.py
import os
import sys

sys.path.append(os.getcwd())

import time
from typing import Optional
from helpers.web3_utils.web3_connector import Web3Connector
from helpers.web3_utils.web3_contract import Web3Contract
from helpers.create_transaction import TransactionState, create_transaction
from helpers.log import get_logger

from helpers.web3_utils.erc20ABI import erc20_ABI

from helpers.load_cloud_env import load_cloud_env

load_cloud_env()

MAX_UINT256 = 2**256 - 1
PROVIDER_URL = os.getenv("PROVIDER_URL")

logger = get_logger(__name__)


def fordefi_sign_and_send_transaction(
    client, chain_id, vault_id, transaction
) -> Optional[str]:
    tx_hash, state = create_transaction(
        client,
        chain_id,
        transaction["data"],
        vault_id,
        transaction["to"],
        transaction["value"],
    )

    if tx_hash is None or state != TransactionState.COMPLETED.value:
        logger.error(f"Signing transaction: {tx_hash} - {state}")
        raise Exception("Signing transaction")

    return tx_hash


def wait_for_transaction_completion(web3_client, transaction_hash):
    transaction_status = web3_client.eth.get_transaction_receipt(transaction_hash)[
        "status"
    ]
    retries = 0
    while transaction_status == 0 and retries < 5:
        time.sleep(1)
        transaction_status = web3_client.eth.get_transaction_receipt(transaction_hash)[
            "status"
        ]
        retries += 1


def build_transaction(web3_client, func, params, account_address):
    """
    Parameters should be in a list if the contract is expecting one or more native data types.
    Parameters should be in a tuple if the contract is expecting a struct.
    """
    retries = 0
    while retries < 5:
        try:
            if isinstance(params, tuple):
                transaction = func(params).build_transaction(
                    {
                        "from": account_address,
                        "nonce": web3_client.eth.get_transaction_count(account_address),
                    }
                )
            elif isinstance(params, list):
                transaction = func(*params).build_transaction(
                    {
                        "from": account_address,
                        "nonce": web3_client.eth.get_transaction_count(account_address),
                    }
                )
            else:
                raise ValueError("Invalid params type")
            return transaction
        except Exception as e:
            logger.warning(f"Retrying transaction build: {e}")
            retries += 1
            time.sleep(1)

    logger.error(f"Failed to build transaction after {retries} retries.")
    return None


def build_and_send_transaction(
    chain_id, vault_id, web3_client, func, params, account_address
):
    transaction = build_transaction(web3_client, func, params, account_address)
    if transaction is None:
        return None
    return fordefi_sign_and_send_transaction(
        web3_client, chain_id, vault_id, transaction
    )


def approve_token(
    chain_id,
    vault_id,
    web3_client,
    token_address: str,
    owner: str,
    to: str,
    amount: int = MAX_UINT256,
):

    logger.info(f"Approving {amount} tokens to {to}")
    token_contract = Web3Contract(
        web3_client,
        token_address,
        erc20_ABI,
    )

    allowance = int(token_contract.call("allowance", [owner, to]))
    if allowance is not None and int(allowance) >= int(amount):
        logger.info(f"Already approved {amount} tokens to {to}")
        return

    transaction_hash = build_and_send_transaction(
        chain_id,
        vault_id,
        web3_client=web3_client,
        func=token_contract.contract.functions.approve,
        params=[to, int(amount)],
        account_address=owner,
    )

    logger.info(f"Approval transaction: {transaction_hash}")


def approve_nft(
    chain_id, vault_id, web3_client, contract: Web3Contract, owner: str, to: str
):

    is_approved_for_all = contract.call("isApprovedForAll", [owner, to])

    if is_approved_for_all:
        logger.info(f"Already approved to {to}")
        return

    transaction_hash = build_and_send_transaction(
        chain_id,
        vault_id,
        web3_client=web3_client,
        func=contract.contract.functions.setApprovalForAll,
        params=[to, True],
        account_address=owner,
    )

    logger.info(f"Approval transaction: {transaction_hash}")


def approve_1155(
    chain_id, vault_id, web3_client, contract: Web3Contract, owner: str, to: str
):
    is_approved_for_all = contract.call("isApprovedForAll", [owner, to])

    if is_approved_for_all:
        logger.info(f"Already approved to {to}")
        return

    transaction_hash = build_and_send_transaction(
        chain_id,
        vault_id,
        web3_client=web3_client,
        func=contract.contract.functions.approveForAll,
        params=[to, True],
        account_address=owner,
    )

    logger.info(f"Approval transaction: {transaction_hash}")
