import logging
import logging.handlers
import os
import sys

log_level = os.environ.get("LOG_LEVEL", "INFO")
logging.getLogger().setLevel(log_level)

console = logging.StreamHandler(sys.stdout)
formater = logging.Formatter("%(asctime)s_%(name)-13s: %(levelname)-8s %(message)s")
console.setFormatter(formater)


def get_logger(name):
    log = logging.getLogger(name)
    log.addHandler(console)
    log.propagate = False
    return log
